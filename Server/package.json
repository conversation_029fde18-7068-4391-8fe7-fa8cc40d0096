{"name": "server", "version": "1.0.0", "main": "server.js", "type": "commonjs", "scripts": {"server": "nodemon server.js", "start": "nodemon server.js", "client": "npm start --prefix ../client", "dev": "concurrently \"npm run server\" \"npm run client\""}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@clerk/express": "^1.3.52", "cloudinary": "^2.5.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "mongoose": "^8.12.1", "multer": "^1.4.5-lts.1", "nodemon": "^3.1.10", "stripe": "^17.7.0", "svix": "^1.42.0"}, "devDependencies": {"concurrently": "^9.1.2"}}