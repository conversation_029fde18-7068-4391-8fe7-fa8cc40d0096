@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');
@import "tailwindcss";

/* Root CSS Variables for consistent theming */
:root {
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  --secondary-50: #f0f9ff;
  --secondary-100: #e0f2fe;
  --secondary-200: #bae6fd;
  --secondary-300: #7dd3fc;
  --secondary-400: #38bdf8;
  --secondary-500: #0ea5e9;
  --secondary-600: #0284c7;
  --secondary-700: #0369a1;
  --secondary-800: #075985;
  --secondary-900: #0c4a6e;

  --accent-50: #fdf4ff;
  --accent-100: #fae8ff;
  --accent-200: #f5d0fe;
  --accent-300: #f0abfc;
  --accent-400: #e879f9;
  --accent-500: #d946ef;
  --accent-600: #c026d3;
  --accent-700: #a21caf;
  --accent-800: #86198f;
  --accent-900: #701a75;

  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;

  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  --border-radius-sm: 0.375rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
}

* {
    font-family: 'Inter', 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    box-sizing: border-box;
}

@layer base {
    html {
        scroll-behavior: smooth;
        font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    }

    body {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%);
        color: var(--gray-800);
        line-height: 1.6;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: 'Poppins', sans-serif;
        font-weight: 600;
        line-height: 1.2;
        color: var(--gray-900);
    }

    h1 { font-size: 2.5rem; font-weight: 700; }
    h2 { font-size: 2rem; font-weight: 600; }
    h3 { font-size: 1.5rem; font-weight: 600; }
    h4 { font-size: 1.25rem; font-weight: 500; }
    h5 { font-size: 1.125rem; font-weight: 500; }
    h6 { font-size: 1rem; font-weight: 500; }
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--border-radius-md);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: var(--border-radius-md);
    transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* Custom utility classes */
@layer utilities {
    .text-gradient {
        background: linear-gradient(135deg, var(--primary-600), var(--secondary-500));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .text-gradient-rainbow {
        background: linear-gradient(135deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        background-size: 200% 200%;
        animation: gradientShift 3s ease infinite;
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    }

    .bg-gradient-secondary {
        background: linear-gradient(135deg, var(--secondary-400), var(--secondary-600));
    }

    .bg-gradient-hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .bg-gradient-elegant {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    }

    .bg-pattern-dots {
        background-image: radial-gradient(circle, rgba(255,255,255,0.3) 1px, transparent 1px);
        background-size: 20px 20px;
    }

    .bg-pattern-grid {
        background-image: linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
                          linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
        background-size: 30px 30px;
    }

    .bg-section-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .bg-section-secondary {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .bg-section-accent {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    .bg-section-neutral {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    }

    /* Enhanced Navbar Styles */
    .navbar-glass {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .navbar-shadow {
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .logo-glow {
        filter: drop-shadow(0 4px 8px rgba(99, 102, 241, 0.3));
    }

    .nav-link-hover {
        position: relative;
        overflow: hidden;
    }

    .nav-link-hover::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
        transition: left 0.5s;
    }

    .nav-link-hover:hover::before {
        left: 100%;
    }

    .shadow-custom {
        box-shadow: var(--shadow-lg);
    }

    .shadow-custom-xl {
        box-shadow: var(--shadow-xl);
    }

    .shadow-colorful {
        box-shadow: 0 10px 40px -10px rgba(255, 107, 107, 0.3);
    }

    .shadow-colorful-blue {
        box-shadow: 0 10px 40px -10px rgba(74, 144, 226, 0.3);
    }

    .shadow-colorful-purple {
        box-shadow: 0 10px 40px -10px rgba(155, 81, 224, 0.3);
    }

    .border-radius-custom {
        border-radius: var(--border-radius-lg);
    }

    .transition-all {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .hover-lift {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .hover-lift:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }

    .hover-rainbow {
        transition: all 0.3s ease;
    }

    .hover-rainbow:hover {
        background: linear-gradient(135deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3);
        background-size: 200% 200%;
        animation: gradientShift 2s ease infinite;
        transform: translateY(-2px);
    }
}

/* Keyframe animations */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes pulse-color {
    0%, 100% {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    25% {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    50% {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    75% {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
}

/* Rich text editor styles */
.rich-text {
    font-size: 15px;
    color: var(--gray-600);
    line-height: 1.7;
}

.rich-text p { margin-bottom: 16px; }

.rich-text h1 {
    font-size: 36px;
    font-weight: 800;
    color: var(--gray-900);
    margin: 32px 0;
}

.rich-text h2 {
    font-size: 22px;
    font-weight: 700;
    color: var(--gray-900);
    margin: 24px 0;
}

.rich-text h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-800);
    margin: 20px 0;
}

.rich-text h4 {
    font-size: 16px;
    font-weight: 500;
    color: var(--gray-700);
    margin: 16px 0;
}

.rich-text h5 {
    font-size: 14px;
    font-weight: 400;
    color: var(--gray-600);
    margin: 12px 0;
}

.rich-text h6 {
    font-size: 12px;
    font-weight: 400;
    color: var(--gray-600);
    margin: 8px 0;
}

.rich-text strong { font-weight: 700; }

.rich-text ol { margin-left: 30px; list-style-type: decimal; }

.rich-text ul { margin-left: 30px; list-style-type: disc; }

.rich-text li { margin-bottom: 8px; }
