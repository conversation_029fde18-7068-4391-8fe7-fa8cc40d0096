import React from "react";
import { Outlet } from "react-router-dom";
import { NavBar } from "../../Components/Educator/NavBar";
import { Sidebar } from "../../Components/Educator/Sidebar";
import { Footers } from "../../Components/Educator/Footers";

export const Educator = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-primary-50/20 flex flex-col font-primary">
      <NavBar />
      <div className="flex flex-1 relative">
        <Sidebar />
        <main className="flex-1 overflow-auto">
          <div className="p-6 md:p-8">
            <Outlet />
          </div>
        </main>
      </div>
      <Footers />
    </div>
  );
};
