import React, { useContext, useEffect, useState } from "react";
import { AppContext } from "../../Context/AppContext";
import { assets, dummyDashboardData } from "../../assets/assets";
import { Loading } from "../../Components/Students/Loading";
import axios from "axios";
import { toast } from "react-toastify";


export const DashBoard = () => {
  const { currency ,backendUrl,isEducator,getToken} = useContext(AppContext);
  const [DashBoardData, setDashboardData] = useState(null);
  const fetchDashboardData = async () => {
    try{
     
      const token=await getToken()
      const {data}=await axios.get(backendUrl + '/api/educator/dashboard',
        {headers : {Authorization: `Bearer ${token}`}}
      )
     
      if(data.success){
        setDashboardData(data.dashboardData)
      }
      else{
        console.log(data.message)
        toast.error(data.message)
      }
    } catch (error){
      toast.error(error.message)
    }
  };
  useEffect(() => {
   if(isEducator){
    fetchDashboardData()
   }
  }, [isEducator]);
  return DashBoardData ? (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2 font-secondary">Dashboard Overview</h1>
        <p className="text-gray-600">Welcome back! Here's what's happening with your courses.</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {/* Total Enrollments Card */}
        <div className="group bg-white rounded-2xl p-6 shadow-soft hover:shadow-large transition-all duration-300 border border-gray-100 hover:border-primary-200 hover-lift">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-primary-600 rounded-xl flex items-center justify-center shadow-colored">
              <img src={assets.patients_icon} alt="enrollments" className="w-6 h-6 filter brightness-0 invert" />
            </div>
            <div className="text-right">
              <div className="text-xs text-gray-500 font-medium uppercase tracking-wide">This Month</div>
              <div className="text-sm text-success-600 font-semibold">+12%</div>
            </div>
          </div>
          <div className="space-y-1">
            <p className="text-3xl font-bold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">
              {DashBoardData.enrolledStudentsData.length}
            </p>
            <p className="text-gray-600 font-medium">Total Enrollments</p>
            <p className="text-sm text-gray-500">Students enrolled in your courses</p>
          </div>
        </div>

        {/* Total Courses Card */}
        <div className="group bg-white rounded-2xl p-6 shadow-soft hover:shadow-large transition-all duration-300 border border-gray-100 hover:border-secondary-200 hover-lift">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-gradient-to-r from-secondary-500 to-secondary-600 rounded-xl flex items-center justify-center shadow-colored">
              <img src={assets.appointments_icon} alt="courses" className="w-6 h-6 filter brightness-0 invert" />
            </div>
            <div className="text-right">
              <div className="text-xs text-gray-500 font-medium uppercase tracking-wide">Active</div>
              <div className="text-sm text-primary-600 font-semibold">{DashBoardData.totalCourses}</div>
            </div>
          </div>
          <div className="space-y-1">
            <p className="text-3xl font-bold text-gray-900 group-hover:text-secondary-600 transition-colors duration-300">
              {DashBoardData.totalCourses}
            </p>
            <p className="text-gray-600 font-medium">Total Courses</p>
            <p className="text-sm text-gray-500">Courses you've created</p>
          </div>
        </div>

        {/* Total Earnings Card */}
        <div className="group bg-white rounded-2xl p-6 shadow-soft hover:shadow-large transition-all duration-300 border border-gray-100 hover:border-success-200 hover-lift md:col-span-2 lg:col-span-1">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-gradient-to-r from-success-500 to-success-600 rounded-xl flex items-center justify-center shadow-colored">
              <img src={assets.earning_icon} alt="earnings" className="w-6 h-6 filter brightness-0 invert" />
            </div>
            <div className="text-right">
              <div className="text-xs text-gray-500 font-medium uppercase tracking-wide">Revenue</div>
              <div className="text-sm text-success-600 font-semibold">+8.2%</div>
            </div>
          </div>
          <div className="space-y-1">
            <p className="text-3xl font-bold text-gray-900 group-hover:text-success-600 transition-colors duration-300">
              {currency}{DashBoardData.totalEarnings}
            </p>
            <p className="text-gray-600 font-medium">Total Earnings</p>
            <p className="text-sm text-gray-500">Revenue from course sales</p>
          </div>
        </div>
      </div>
      {/* Recent Enrollments Section */}
      <div className="bg-white rounded-2xl shadow-soft border border-gray-100">
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 font-secondary">Recent Enrollments</h2>
              <p className="text-gray-600 text-sm mt-1">Latest students who joined your courses</p>
            </div>
            <button className="text-primary-600 hover:text-primary-700 font-medium text-sm hover:bg-primary-50 px-3 py-2 rounded-lg transition-colors duration-200">
              View All
            </button>
          </div>
        </div>

        <div className="overflow-hidden">
          {DashBoardData.enrolledStudentsData.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50/50">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider hidden sm:table-cell">
                      #
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Student
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Course
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider hidden md:table-cell">
                      Enrolled
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-100">
                  {DashBoardData.enrolledStudentsData.slice(0, 5).map((item, index) => (
                    <tr key={index} className="hover:bg-gray-50/50 transition-colors duration-200">
                      <td className="px-6 py-4 text-sm text-gray-500 hidden sm:table-cell">
                        <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center text-primary-600 font-semibold text-xs">
                          {index + 1}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center space-x-3">
                          <img
                            src={item.student.imageUrl}
                            alt="Profile"
                            className="w-10 h-10 rounded-full ring-2 ring-gray-200 object-cover"
                          />
                          <div>
                            <p className="text-sm font-semibold text-gray-900">{item.student.name}</p>
                            <p className="text-xs text-gray-500">{item.student.email || '<EMAIL>'}</p>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="max-w-xs">
                          <p className="text-sm font-medium text-gray-900 truncate">{item.courseTitle}</p>
                          <p className="text-xs text-gray-500">Course ID: #{item.courseId?.slice(-6) || 'N/A'}</p>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500 hidden md:table-cell">
                        {new Date(item.enrolledAt || Date.now()).toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric',
                          year: 'numeric'
                        })}
                      </td>
                      <td className="px-6 py-4">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 text-success-800">
                          <div className="w-1.5 h-1.5 bg-success-500 rounded-full mr-1.5"></div>
                          Active
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No enrollments yet</h3>
              <p className="text-gray-600 mb-4">Students will appear here once they enroll in your courses.</p>
              <button className="bg-gradient-primary text-white px-4 py-2 rounded-lg font-medium hover:shadow-colored transition-all duration-300">
                Create Your First Course
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  ) : (
    <Loading />
  );
};
