import React, { useContext, useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { AppContext } from "../../Context/AppContext";
import { Loading } from "../../Components/Students/Loading";
import { assets } from "../../assets/assets";
import humanizeDuration from "humanize-duration";
import { Footer } from "../../Components/Students/Footer";
import YouTube from "react-youtube";
import axios from "axios";
import { toast } from "react-toastify";

export const CourseDetails = () => {
  const { id } = useParams();
  const [courseData, setCourseData] = useState(null);
  const [openSections, setOpenSections] = useState([]);
  const [isAlreadyEnrolled, setisAlreadyEnrolled] = useState(false);
  const [playerData, setPlayerData] = useState(null);
  const {
    allCourses,
    calculateRating,
    calculateNoOfLectures,
    calculateCourseDuration,
    calculateChapterTime,
    currency,
    backendUrl,userData,getToken
  } = useContext(AppContext);

  const fetchCourseData = async () => {
   try{
     
    const {data}=await axios.get(backendUrl + '/api/course/' + id)
    if(data.success){
      setCourseData(data.courseData)
    } else{
      toast.error(data.message)
    }

   } catch (error){
      toast.error(data.message)
   }
  };

  const enrollCourse=async ()=>{
    try{
      if(!userData){
        return toast.warn('Login to Enroll')
      }
      if(isAlreadyEnrolled){
        return toast.warn('Already Enrolled')
      }
      const token = await getToken();

      const {data}=await axios.post(backendUrl+'/api/user/purchase',{courseId:courseData._id},{headers:{Authorization:`Bearer ${token}`}})
     
      if(data.success){
        const {session_url}=data
        window.location.replace(session_url)
    } else{
      toast.error(data.message)
    }
    } catch (error){
      toast.error(error.message)
    }
  }

  useEffect(() => {
   
    fetchCourseData();
   
  },[]);
  useEffect(() => {
   
    if(userData && courseData){
      setisAlreadyEnrolled(userData?.enrolledCourses?.includes(courseData._id))
    }
   
  },[userData,courseData]);

  const toggleSection = (index) => {
    setOpenSections((prev) => ({
      ...prev,
      [index]: !prev[index],
    }));
  };
  return courseData ? (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
            {/* Course Info - Left Column */}
            <div className="lg:col-span-2 space-y-6">
              <div>
                <h1 className="text-4xl lg:text-5xl font-bold mb-4 leading-tight">
                  {courseData.courseTitle}
                </h1>
                <div
                  className="text-lg text-blue-100 leading-relaxed"
                  dangerouslySetInnerHTML={{
                    __html: courseData.courseDescription.slice(0, 250) + "...",
                  }}
                />
              </div>

              {/* Course Stats */}
              <div className="flex flex-wrap items-center gap-6 text-sm">
                <div className="flex items-center gap-2">
                  <div className="flex items-center">
                    <span className="text-yellow-400 font-bold text-lg">
                      {calculateRating(courseData)}
                    </span>
                    <div className="flex ml-2">
                      {[...Array(5)].map((_, i) => (
                        <span
                          key={i}
                          className={`text-lg ${
                            i < Math.floor(calculateRating(courseData))
                              ? "text-yellow-400"
                              : "text-gray-400"
                          }`}
                        >
                          ★
                        </span>
                      ))}
                    </div>
                  </div>
                  <span className="text-blue-200">
                    ({courseData.courseRatings.length} {courseData.courseRatings.length === 1 ? "rating" : "ratings"})
                  </span>
                </div>

                <div className="h-4 w-px bg-blue-300"></div>

                <div className="flex items-center gap-2">
                  <span className="text-blue-200">👥</span>
                  <span className="text-blue-100">
                    {courseData?.enrolledStudents?.length || 0} {courseData?.enrolledStudents?.length === 1 ? "student" : "students"}
                  </span>
                </div>

                <div className="h-4 w-px bg-blue-300"></div>

                <div className="flex items-center gap-2">
                  <span className="text-blue-200">🎓</span>
                  <span className="text-blue-100">
                    By <span className="font-semibold text-white">{courseData?.educator?.name}</span>
                  </span>
                </div>
              </div>

              {/* Course Features */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4">
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 text-center">
                  <div className="text-2xl mb-1">🕒</div>
                  <div className="text-xs text-blue-200">Duration</div>
                  <div className="font-semibold">{calculateCourseDuration(courseData)}</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 text-center">
                  <div className="text-2xl mb-1">📚</div>
                  <div className="text-xs text-blue-200">Lessons</div>
                  <div className="font-semibold">{calculateNoOfLectures(courseData)}</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 text-center">
                  <div className="text-2xl mb-1">📜</div>
                  <div className="text-xs text-blue-200">Certificate</div>
                  <div className="font-semibold">Included</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 text-center">
                  <div className="text-2xl mb-1">♾️</div>
                  <div className="text-xs text-blue-200">Access</div>
                  <div className="font-semibold">Lifetime</div>
                </div>
              </div>
            </div>

            {/* Course Thumbnail - Right Column */}
            <div className="lg:col-span-1">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                <img
                  src={courseData.courseThumbnail}
                  alt={courseData.courseTitle}
                  className="w-full h-48 object-cover rounded-xl mb-6"
                />

                {/* Enrollment Section */}
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold mb-2">
                      {courseData.coursePrice > 0 ? `$${courseData.coursePrice}` : "Free"}
                    </div>
                    {courseData.coursePrice > 0 && (
                      <div className="text-blue-200 text-sm line-through">
                        ${(courseData.coursePrice * 1.5).toFixed(2)}
                      </div>
                    )}
                  </div>

                  {isEnrolled ? (
                    <button
                      onClick={() => navigate(`/player/${courseData._id}`)}
                      className="w-full bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg"
                    >
                      🎯 Continue Learning
                    </button>
                  ) : (
                    <button
                      onClick={enrollCourse}
                      className="w-full bg-white text-blue-600 hover:bg-blue-50 font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg"
                    >
                      🚀 Enroll Now
                    </button>
                  )}

                  <div className="text-center text-xs text-blue-200">
                    💯 30-day money-back guarantee
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Course Content Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="px-8 py-6 bg-gradient-to-r from-gray-50 to-white border-b border-gray-200">
            <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
              📚 Course Structure
            </h2>
            <p className="text-gray-600 mt-2">
              {courseData?.courseContent?.length || 0} chapters • {calculateNoOfLectures(courseData)} lectures • {calculateCourseDuration(courseData)}
            </p>
          </div>

          <div className="p-8">
            {courseData?.courseContent?.map((chapter, index) => (
              <div
                key={index}
                className="border border-gray-200 bg-white mb-4 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200"
              >
                <div
                  className="flex items-center justify-between px-6 py-4 cursor-pointer select-none hover:bg-gray-50 transition-colors duration-200"
                  onClick={() => toggleSection(index)}
                >
                  <div className="flex items-center gap-3">
                    <div className="flex-shrink-0">
                      <span className={`text-gray-500 transform transition-transform duration-200 ${
                        openSections[index] ? "rotate-90" : ""
                      }`}>
                        ▶
                      </span>
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900 text-lg">{chapter.chapterTitle}</p>
                      <p className="text-sm text-gray-500 mt-1">
                        {chapter.chapterContent.length} lectures • {calculateChapterTime(chapter)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-gray-400">
                      {openSections[index] ? "Hide" : "Show"} content
                    </div>
                  </div>
                </div>
                <div
                  className={`overflow-hidden transition-all duration-300 ${
                    openSections[index] ? "max-h-96" : "max-h-0"
                  }`}
                >
                  <div className="px-6 pb-4 border-t border-gray-100">
                    <div className="space-y-2 pt-4">
                      {chapter.chapterContent.map((lecture, i) => (
                        <div key={i} className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                          <div className="flex-shrink-0">
                            <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                              <span className="text-blue-600 text-xs">▶</span>
                            </div>
                          </div>

                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {lecture.lectureTitle}
                            </p>
                          </div>

                          <div className="flex items-center gap-3">
                            {lecture.isPreviewFree && (
                              <button
                                onClick={() =>
                                  setPlayerData({
                                    videoId: lecture.lectureUrl.split("/").pop(),
                                  })
                                }
                                className="text-xs text-blue-600 hover:text-blue-800 font-medium"
                              >
                                Preview
                              </button>
                            )}
                            <span className="text-xs text-gray-500">
                              {humanizeDuration(lecture.lectureDuration * 60 * 1000, { units: ["h", "m"] })}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      {/* Course Description Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Description - Left Column */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-3">
                📖 Course Description
              </h3>
              <div
                className="prose prose-blue max-w-none text-gray-700 leading-relaxed"
                dangerouslySetInnerHTML={{
                  __html: courseData.courseDescription,
                }}
              />
            </div>
          </div>

          {/* Preview Video - Right Column */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden sticky top-8">
              {playerData ? (
                <div className="aspect-video">
                  <YouTube
                    videoId={playerData.videoId}
                    opts={{
                      width: '100%',
                      height: '100%',
                      playerVars: {
                        autoplay: 1,
                        controls: 1,
                        rel: 0,
                      },
                    }}
                    className="w-full h-full"
                  />
                </div>
              ) : (
                <div className="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                  <div className="text-center">
                    <img
                      src={courseData.courseThumbnail}
                      alt={courseData.courseTitle}
                      className="w-24 h-24 rounded-full object-cover mx-auto mb-4 shadow-lg"
                    />
                    <p className="text-gray-600 font-medium">Course Preview</p>
                    <p className="text-sm text-gray-500">Select a preview to watch</p>
                  </div>
                </div>
              )}

              <div className="p-6">
                <div className="text-center mb-6">
                  <div className="text-3xl font-bold text-gray-900 mb-2">
                    {courseData.coursePrice > 0 ? `${currency}${(courseData.coursePrice - (courseData.discount * courseData.coursePrice) / 100).toFixed(2)}` : "Free"}
                  </div>
                  {courseData.coursePrice > 0 && (
                    <div className="flex items-center justify-center gap-2 text-sm">
                      <span className="text-gray-500 line-through">
                        {currency}{courseData.coursePrice}
                      </span>
                      <span className="text-green-600 font-medium">
                        {courseData.discount}% off
                      </span>
                    </div>
                  )}
                  <div className="text-xs text-red-500 mt-2 flex items-center justify-center gap-1">
                    🔥 Limited time offer
                  </div>
                </div>

                {isEnrolled ? (
                  <button
                    onClick={() => navigate(`/player/${courseData._id}`)}
                    className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg mb-4"
                  >
                    🎯 Continue Learning
                  </button>
                ) : (
                  <button
                    onClick={enrollCourse}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg mb-4"
                  >
                    🚀 Enroll Now
                  </button>
                )}

                <div className="space-y-3 text-sm text-gray-600 mb-4">
                  <div className="flex items-center gap-2">
                    <span>⭐</span>
                    <span>{calculateRating(courseData)} rating</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>🕒</span>
                    <span>{calculateCourseDuration(courseData)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>📚</span>
                    <span>{calculateNoOfLectures(courseData)} lessons</span>
                  </div>
                </div>

                <div className="border-t border-gray-200 pt-4">
                  <h4 className="font-semibold text-gray-900 mb-3">What's included:</h4>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li className="flex items-center gap-2">
                      <span className="text-green-500">✓</span>
                      Lifetime access with free updates
                    </li>
                    <li className="flex items-center gap-2">
                      <span className="text-green-500">✓</span>
                      Downloadable resources
                    </li>
                    <li className="flex items-center gap-2">
                      <span className="text-green-500">✓</span>
                      Certificate of completion
                    </li>
                    <li className="flex items-center gap-2">
                      <span className="text-green-500">✓</span>
                      30-day money-back guarantee
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  ) : (
    <Loading />
  );
};
