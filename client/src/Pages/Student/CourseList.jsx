import React, { useContext, useEffect, useState } from 'react'
import { AppContext } from '../../Context/AppContext'
import { useNavigate, useParams } from 'react-router-dom'
import { SearchBar } from '../../Components/Students/SearchBar'
import { CourseCard } from '../../Components/Students/CourseCard'
import { assets } from '../../assets/assets'
import { Footer } from '../../Components/Students/Footer'

export const CourseList = () => {
  const {allCourses}=useContext(AppContext)
  const navigate = useNavigate();
  const {input}=useParams();  
  const [filteredCourse,setFilteredCourse]=useState([])
  useEffect(()=>{
    if(allCourses && allCourses.length>0){
      const tempCourses=allCourses.slice()
      input ? 
      setFilteredCourse(tempCourses.filter(item=> item.courseTitle.toLowerCase().includes(input.toLowerCase())))
      :setFilteredCourse(tempCourses)
    }
  },[allCourses,input])

  return (
    <>
      <div className='min-h-screen bg-gradient-to-br from-slate-50 to-indigo-50'>
        {/* Header Section */}
        <div className='relative md:px-36 px-8 pt-24 pb-12'>
          {/* Background Pattern */}
          <div className='absolute top-10 right-10 w-64 h-64 bg-gradient-to-r from-indigo-100 to-purple-100 rounded-full blur-3xl opacity-60'></div>
          <div className='absolute bottom-10 left-10 w-80 h-80 bg-gradient-to-r from-cyan-100 to-blue-100 rounded-full blur-3xl opacity-60'></div>

          <div className='relative'>
            {/* Breadcrumb and Title */}
            <div className='flex md:flex-row flex-col gap-8 items-start justify-between w-full mb-8'>
              <div className='space-y-4'>
                {/* Breadcrumb */}
                <nav className='flex items-center space-x-2 text-sm'>
                  <button
                    onClick={() => navigate('/')}
                    className='text-indigo-600 hover:text-indigo-700 font-medium transition-colors duration-200'
                  >
                    Home
                  </button>
                  <svg className='w-4 h-4 text-gray-400' fill='currentColor' viewBox='0 0 20 20'>
                    <path fillRule='evenodd' d='M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z' clipRule='evenodd' />
                  </svg>
                  <span className='text-gray-600 font-medium'>Course List</span>
                </nav>

                {/* Title */}
                <div>
                  <h1 className='text-4xl md:text-5xl font-bold text-gray-900 mb-3 font-secondary'>
                    Discover{' '}
                    <span className='bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent'>
                      Amazing
                    </span>{' '}
                    Courses
                  </h1>
                  <p className='text-lg text-gray-600 max-w-2xl'>
                    Explore our comprehensive collection of courses designed to help you master new skills and advance your career.
                  </p>
                </div>
              </div>

              {/* Search Bar */}
              <div className='w-full md:w-auto md:min-w-96'>
                <SearchBar data={input}/>
              </div>
            </div>

            {/* Active Filter Tag */}
            {input && (
              <div className='inline-flex items-center gap-3 px-4 py-2 bg-white border border-primary-200 rounded-full shadow-soft mb-8 animate-slide-down'>
                <span className='text-sm font-medium text-gray-700'>Searching for:</span>
                <span className='text-sm font-semibold text-primary-600 bg-primary-50 px-3 py-1 rounded-full'>
                  {input}
                </span>
                <button
                  onClick={() => navigate('/course-list')}
                  className='p-1 hover:bg-gray-100 rounded-full transition-colors duration-200'
                >
                  <img src={assets.cross_icon} alt='Clear filter' className='w-4 h-4' />
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Course Grid Section */}
        <div className='md:px-36 px-8 pb-20'>
          {/* Results Count */}
          <div className='flex items-center justify-between mb-8'>
            <p className='text-gray-600'>
              <span className='font-semibold text-gray-800'>{filteredCourse.length}</span> courses found
              {input && <span> for "{input}"</span>}
            </p>

            {/* Sort Options */}
            <div className='flex items-center gap-4'>
              <span className='text-sm text-gray-600'>Sort by:</span>
              <select className='bg-white border border-gray-200 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-200 focus:border-indigo-300'>
                <option>Most Popular</option>
                <option>Newest</option>
                <option>Price: Low to High</option>
                <option>Price: High to Low</option>
                <option>Highest Rated</option>
              </select>
            </div>
          </div>

          {/* Course Grid */}
          {filteredCourse.length > 0 ? (
            <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8'>
              {filteredCourse.map((course, index) => (
                <div
                  key={index}
                  className='animate-slide-up'
                  style={{animationDelay: `${index * 0.05}s`}}
                >
                  <CourseCard course={course} />
                </div>
              ))}
            </div>
          ) : (
            /* No Results */
            <div className='text-center py-20'>
              <div className='w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6'>
                <svg className='w-12 h-12 text-gray-400' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                  <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z' />
                </svg>
              </div>
              <h3 className='text-xl font-semibold text-gray-800 mb-2'>No courses found</h3>
              <p className='text-gray-600 mb-6'>
                We couldn't find any courses matching "{input}". Try adjusting your search terms.
              </p>
              <button
                onClick={() => navigate('/course-list')}
                className='bg-gradient-primary text-white px-6 py-3 rounded-xl font-semibold hover:shadow-colored transition-all duration-300'
              >
                View All Courses
              </button>
            </div>
          )}
        </div>
      </div>
      <Footer/>
    </>
  )
}
