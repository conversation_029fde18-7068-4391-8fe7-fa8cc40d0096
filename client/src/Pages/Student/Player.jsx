import React, { useContext, useEffect, useState } from "react";
import { AppContext } from "../../Context/AppContext";
import { useParams } from "react-router-dom";
import { assets } from "../../assets/assets";
import humanizeDuration from "humanize-duration";
import YouTube from "react-youtube";
import { Footer } from "../../Components/Students/Footer";
import { Rating } from "../../Components/Students/Rating";
import axios from "axios";
import { toast } from "react-toastify";
import { Loading } from "../../Components/Students/Loading";

export const Player = () => {
  const { enrolledCourses, calculateChapterTime, backendUrl, getToken, userData, fetchUserEnrolledCourses } = useContext(AppContext);
  const { courseId } = useParams();

  const [courseData, setCourseData] = useState(null);
  const [openSections, setOpenSections] = useState({});
  const [playerData, setPlayerData] = useState(null);
  const [progressData, setProgressData] = useState(null);
  const [initialRating, setInitialRating] = useState(0);

  const getCourseData = () => {
    enrolledCourses.map((course) => {
      if (course._id === courseId) {
        setCourseData(course);
        course.courseRatings.map((item) => {
          if (item.userId === userData._id) {
            setInitialRating(item.rating);
          }
        });
      }
    });
  };

  useEffect(() => {
    if (enrolledCourses.length > 0) {
      getCourseData();
    }
  }, [enrolledCourses]);

  const markLectureCompleted = async (lectureId) => {
    try {
      const token = await getToken();
      const { data } = await axios.post(
        backendUrl + '/api/user/update-course-progress',
        { courseId, lectureId },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      if (data.success) {
        toast.success(data.message);
        getCourseProgress();
      } else {
        toast.error(data.message);
      }
    } catch (error) {
      toast.error(error.message);
    }
  };

  const handleRate = async (rating) => {
    try {
      const token = await getToken();
      const { data } = await axios.post(
        backendUrl + '/api/user/add-rating',
        { courseId, rating },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      if (data.success) {
        toast.success(data.message);
        fetchUserEnrolledCourses();
      } else {
        toast.error(data.message);
      }
    } catch (error) {
      toast.error(error.message);
    }
  };

  const getCourseProgress = async () => {
    try {
      const token = await getToken();
      const { data } = await axios.post(
        backendUrl + '/api/user/get-course-progress',
        { courseId },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      if (data.success) {
        setProgressData(data.progressData);
      } else {
        toast.error(data.message);
      }
    } catch (error) {
      toast.error(error.message);
    }
  };

  const getYouTubeID = (url) => {
    if (!url) return null;

    // Handle different YouTube URL formats
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/)([^#&?]*)/,
      /^([a-zA-Z0-9_-]{11})$/ // Direct video ID
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match && match[1] && match[1].length === 11) {
        return match[1];
      }
    }

    console.warn('Could not extract YouTube video ID from URL:', url);
    return null;
  };

  const toggleSection = (index) => {
    setOpenSections((prev) => ({
      ...prev,
      [index]: !prev[index],
    }));
  };

  useEffect(() => {
    getCourseProgress();
  }, []);

  return courseData ? (
    <>
      <div className="p-4 sm:p-10 grid grid-cols-1 md:grid-cols-2 gap-10 md:px-36 bg-gradient-to-b from bg-cyan-50 mt-12">
        {/* left column */}
        <div className="text-gray-800">
          <h2 className="text-2xl font-semibold mb-4">Course Structure</h2>
          <div className="pt-5">
            {courseData &&
              courseData.courseContent.map((chapter, index) => (
                <div
                  key={index}
                  className="border border-gray-300 bg-white mb-4 rounded-lg shadow-lg"
                >
                  <div
                    className="flex items-center justify-between px-4 py-3 cursor-pointer select-none bg-gray-100 hover:bg-gray-200 transition-colors duration-200"
                    onClick={() => toggleSection(index)}
                  >
                    <div className="flex items-center gap-2">
                      <img
                        src={assets.down_arrow_icon}
                        alt="arrow icon"
                        className={`transform transition-transform ${
                          openSections[index] ? "rotate-180" : ""
                        }`}
                      />
                      <p className="font-medium text-lg">
                        {chapter.chapterTitle}
                      </p>
                    </div>
                    <p className="px-4 pb-3 text-sm text-gray-600">
                      {chapter.chapterContent.length} lectures -{" "}
                      {calculateChapterTime(chapter)}
                    </p>
                  </div>
                  <div
                    className={`overflow-hidden transition-all duration-300 ${
                      openSections[index] ? "max-h-96" : "max-h-0"
                    }`}
                  >
                    <ul className="list-disc md:pl-10 pl-4 pr-4 text-gray-600 border-t border-gray-300">
                      {chapter.chapterContent.map((lecture, i) => (
                        <li key={i} className="flex items-start gap-2 py-2">
                          <img
                            src={
                              progressData && progressData.lectureCompleted.includes(lecture.lectureId) ? assets.blue_tick_icon : assets.play_icon
                            }
                            alt="play icon"
                            className="w-4 h-4 mt-1"
                          />
                          <div className="flex items-center justify-between w-full text-gray-800 text-sm md:text-base">
                            <p>{lecture.lectureTitle}</p>
                            <div className="flex gap-2">
                              {lecture.lectureUrl && (
                                <p
                                  onClick={() => {
                                    console.log('Setting player data for lecture:', lecture.lectureTitle, 'URL:', lecture.lectureUrl);
                                    setPlayerData({
                                      ...lecture,
                                      chapter: index + 1,
                                      lecture: i + 1,
                                      lectureId: lecture.lectureId ?? lecture._id
                                    });
                                  }}
                                  className="text-blue-500 cursor-pointer hover:underline"
                                >
                                  Watch
                                </p>
                              )}
                              <p>
                                {humanizeDuration(
                                  lecture.lectureDuration * 60 * 1000,
                                  { units: ["h", "m"] }
                                )}
                              </p>
                            </div>
                          </div>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
          </div>
          <div className="flex"> 
            <h1 className="text-xl font-bold">Rate this Course:</h1>
            <Rating initialRating={initialRating} onRate={handleRate}/>
          </div>
        </div>
        {/* right column */}
        <div className="flex justify-center items-center">
          {playerData ? (
            <div className="w-full max-w-4xl">
              {getYouTubeID(playerData.lectureUrl) ? (
                <YouTube
                  videoId={getYouTubeID(playerData.lectureUrl)}
                  opts={{
                    width: '100%',
                    height: '400',
                    playerVars: {
                      autoplay: 1,
                      controls: 1,
                      rel: 0,
                      showinfo: 0,
                      modestbranding: 1,
                    },
                  }}
                  className="w-full"
                  onError={(error) => {
                    console.error('YouTube Player Error:', error);
                    toast.error('Error loading video. Please check the video URL.');
                  }}
                />
              ) : (
                <div className="w-full h-96 bg-gray-200 flex items-center justify-center rounded-lg">
                  <div className="text-center">
                    <div className="text-4xl mb-4">⚠️</div>
                    <p className="text-lg mb-2 text-gray-700">Invalid Video URL</p>
                    <p className="text-sm text-gray-500">Please contact your instructor to fix this video link.</p>
                    <p className="text-xs text-gray-400 mt-2">URL: {playerData.lectureUrl}</p>
                  </div>
                </div>
              )}

              <div className="flex justify-between items-center mt-4 p-4 bg-white rounded-lg shadow-sm">
                <div>
                  <h3 className="font-semibold text-gray-900">
                    {playerData.chapter}.{playerData.lecture} {playerData.lectureTitle}
                  </h3>
                  <p className="text-sm text-gray-600">
                    Duration: {humanizeDuration(playerData.lectureDuration * 60 * 1000, { units: ["h", "m"] })}
                  </p>
                </div>
                <button
                  onClick={() => markLectureCompleted(playerData.lectureId)}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                    progressData && progressData.lectureCompleted.includes(playerData.lectureId)
                      ? 'bg-green-100 text-green-700 border border-green-200'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  {progressData && progressData.lectureCompleted.includes(playerData.lectureId) ? '✓ Completed' : 'Mark Complete'}
                </button>
              </div>
            </div>
          ) : (
            <img
              src={courseData ? courseData.courseThumbnail : " "}
              alt={
                courseData
                  ? `${courseData.courseTitle} Thumbnail`
                  : "Course Thumbnail"
              }
              className="max-w-full h-auto rounded-lg shadow-lg"
            />
          )}
        </div>
      </div>
      <Footer/>
    </>
  ) : <Loading/>
};