import React, { useContext, useEffect, useState } from "react";

import { AppContext } from "../../Context/AppContext";
import { useParams } from "react-router-dom";
import { assets } from "../../assets/assets";
import humanizeDuration from "humanize-duration";
import YouTube from "react-youtube";
import { Footer } from "../../Components/Students/Footer";
import { Rating } from "../../Components/Students/Rating";
import axios from "axios";
import { toast } from "react-toastify";
import { Loading } from "../../Components/Students/Loading";

export const Player = () => {
  const { enrolledCourses, calculateChapterTime, backendUrl, getToken, userData, fetchUserEnrolledCourses } = useContext(AppContext);
  const { courseId } = useParams();

  const [courseData, setCourseData] = useState(null);
  const [openSections, setOpenSections] = useState({});
  const [playerData, setPlayerData] = useState(null);
  const [progressData, setProgressData] = useState(null);
  const [initialRating, setInitialRating] = useState(0);

  const getCourseData = () => {
    enrolledCourses.map((course) => {
      if (course._id === courseId) {
        setCourseData(course);
        course.courseRatings.map((item) => {
          if (item.userId === userData._id) {
            setInitialRating(item.rating);
          }
        });
      }
    });
  };

  const getYouTubeID = (url) => {
    if (!url) return null;

    // Handle different YouTube URL formats
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/)([^#&?]*)/,
      /^([a-zA-Z0-9_-]{11})$/ // Direct video ID
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match && match[1] && match[1].length === 11) {
        return match[1];
      }
    }

    console.warn('Could not extract YouTube video ID from URL:', url);
    return null;
  };

  useEffect(() => {
    if (enrolledCourses.length > 0) {
      getCourseData();
    }
  }, [enrolledCourses]);

  const markLectureCompleted = async (lectureId) => {
    try {
      const token = await getToken();
      const { data } = await axios.post(
        backendUrl + '/api/user/update-course-progress',
        { courseId, lectureId },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      if (data.success) {
        toast.success(data.message);
        getCourseProgress();
      } else {
        toast.error(data.message);
      }
    } catch (error) {
      toast.error(error.message);
    }
  };

  const handleRate = async (rating) => {
    try {
      const token = await getToken();
      const { data } = await axios.post(
        backendUrl + '/api/user/add-rating',
        { courseId, rating },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      if (data.success) {
        toast.success(data.message);
        fetchUserEnrolledCourses();
      } else {
        toast.error(data.message);
      }
    } catch (error) {
      toast.error(error.message);
    }
  };

  const getCourseProgress = async () => {
    try {
      const token = await getToken();
      const { data } = await axios.post(
        backendUrl + '/api/user/get-course-progress',
        { courseId },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      if (data.success) {
        setProgressData(data.progressData);
      } else {
        toast.error(data.message);
      }
    } catch (error) {
      toast.error(error.message);
    }
  };

  const toggleSection = (index) => {
    setOpenSections((prev) => ({
      ...prev,
      [index]: !prev[index],
    }));
  };

  useEffect(() => {
    getCourseProgress();
  }, []);

  return courseData ? (
    <>
      <div className="min-h-screen  bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        {/* Course Header */}
        <div className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between mt-16">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">{courseData.courseTitle}</h1>
                <p className="text-gray-600">Continue your learning journey</p>
              </div>
              <div className="flex items-center gap-4">
                <div className="text-right">
                  <p className="text-sm text-gray-500">Progress</p>
                  <div className="flex items-center gap-2">
                    <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-gradient-to-r from-blue-500 to-indigo-600 transition-all duration-300"
                        style={{
                          width: `${progressData ?
                            (progressData.lectureCompleted.length /
                            courseData.courseContent.reduce((acc, chapter) => acc + chapter.chapterContent.length, 0)) * 100
                            : 0}%`
                        }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium text-gray-700">
                      {progressData ?
                        `${progressData.lectureCompleted.length}/${courseData.courseContent.reduce((acc, chapter) => acc + chapter.chapterContent.length, 0)}`
                        : '0/0'
                      }
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Course Structure - Left Column */}
            <div className="lg:col-span-1 space-y-6">
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <div className="px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600">
                  <h2 className="text-xl font-semibold text-white flex items-center gap-2">
                    📚 Course Structure
                  </h2>
                </div>

                <div className="max-h-96 overflow-y-auto">
                  {courseData &&
                    courseData.courseContent.map((chapter, index) => (
                      <div key={index} className="border-b border-gray-100 last:border-b-0">
                        <div
                          className="flex items-center justify-between px-6 py-4 cursor-pointer select-none hover:bg-gray-50 transition-colors duration-200"
                          onClick={() => toggleSection(index)}
                        >
                          <div className="flex items-center gap-3">
                            <div className="flex-shrink-0">
                              <span className={`text-gray-500 transform transition-transform duration-200 ${
                                openSections[index] ? "rotate-90" : ""
                              }`}>
                                ▶
                              </span>
                            </div>
                            <div>
                              <p className="font-medium text-gray-900 text-sm">
                                {chapter.chapterTitle}
                              </p>
                              <p className="text-xs text-gray-500 mt-1">
                                {chapter.chapterContent.length} lectures • {calculateChapterTime(chapter)}
                              </p>
                            </div>
                          </div>
                        </div>
                        <div
                          className={`overflow-hidden transition-all duration-300 ${
                            openSections[index] ? "max-h-96" : "max-h-0"
                          }`}
                        >
                          <div className="px-6 pb-4">
                            <div className="space-y-2">
                              {chapter.chapterContent.map((lecture, i) => (
                                <div key={i} className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                  <div className="flex-shrink-0">
                                    {progressData && progressData.lectureCompleted.includes(lecture.lectureId) ? (
                                      <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                                        <span className="text-green-600 text-sm">✓</span>
                                      </div>
                                    ) : (
                                      <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                                        <span className="text-blue-600 text-xs">▶</span>
                                      </div>
                                    )}
                                  </div>

                                  <div className="flex-1 min-w-0">
                                    <p className="text-sm font-medium text-gray-900 truncate">
                                      {lecture.lectureTitle}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                      {humanizeDuration(lecture.lectureDuration * 60 * 1000, { units: ["h", "m"] })}
                                    </p>
                                  </div>

                                  {lecture.lectureUrl && (
                                    <button
                                      onClick={() => {
                                        console.log('Setting player data for lecture:', lecture.lectureTitle, 'URL:', lecture.lectureUrl);
                                        setPlayerData({
                                          ...lecture,
                                          chapter: index + 1,
                                          lecture: i + 1,
                                          lectureId: lecture.lectureId ?? lecture._id
                                        });
                                      }}
                                      className="px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded-full hover:bg-blue-100 transition-colors duration-200"
                                    >
                                      {progressData && progressData.lectureCompleted.includes(lecture.lectureId) ? 'Rewatch' : 'Watch'}
                                    </button>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </div>

              {/* Rating Section */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  ⭐ Rate this Course
                </h3>
                <Rating initialRating={initialRating} onRate={handleRate}/>
              </div>
            </div>
            {/* Video Player - Right Column */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                {playerData ? (
                  <div>
                    {/* Video Player */}
                    <div className="aspect-video bg-black">
                      {getYouTubeID(playerData.lectureUrl) ? (
                        <YouTube
                          videoId={getYouTubeID(playerData.lectureUrl)}
                          opts={{
                            width: '100%',
                            height: '100%',
                            playerVars: {
                              autoplay: 1,
                              controls: 1,
                              rel: 0,
                              showinfo: 0,
                              modestbranding: 1,
                            },
                          }}
                          className="w-full h-full"
                          onError={(error) => {
                            console.error('YouTube Player Error:', error);
                            toast.error('Error loading video. Please check the video URL.');
                          }}
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-white">
                          <div className="text-center">
                            <div className="text-4xl mb-4">⚠️</div>
                            <p className="text-lg mb-2">Invalid Video URL</p>
                            <p className="text-sm opacity-75">Please contact your instructor to fix this video link.</p>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Video Info */}
                    <div className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-2">
                            {playerData.lectureTitle}
                          </h3>
                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            <span className="flex items-center gap-1">
                              🕒 {humanizeDuration(playerData.lectureDuration * 60 * 1000, { units: ["h", "m"] })}
                            </span>
                            <span className="flex items-center gap-1">
                              📖 Chapter {playerData.chapter}, Lecture {playerData.lecture}
                            </span>
                          </div>
                        </div>

                        <button
                          onClick={() => markLectureCompleted(playerData.lectureId)}
                          className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                            progressData && progressData.lectureCompleted.includes(playerData.lectureId)
                              ? 'bg-green-100 text-green-700 border border-green-200'
                              : 'bg-blue-600 text-white hover:bg-blue-700 shadow-sm'
                          }`}
                        >
                          {progressData && progressData.lectureCompleted.includes(playerData.lectureId)
                            ? (
                              <span className="flex items-center gap-2">
                                ✓ Completed
                              </span>
                            ) : (
                              <span className="flex items-center gap-2">
                                ✓ Mark Complete
                              </span>
                            )
                          }
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                    <div className="text-center">
                      <div className="w-24 h-24 mx-auto mb-4 bg-white rounded-full shadow-lg flex items-center justify-center">
                        <img
                          src={courseData ? courseData.courseThumbnail : ""}
                          alt={courseData ? `${courseData.courseTitle} Thumbnail` : "Course Thumbnail"}
                          className="w-16 h-16 rounded-full object-cover"
                        />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-700 mb-2">
                        Welcome to {courseData?.courseTitle}
                      </h3>
                      <p className="text-gray-500">
                        Select a lecture from the course structure to start learning
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer/>
    </>
  ) : <Loading/>
};