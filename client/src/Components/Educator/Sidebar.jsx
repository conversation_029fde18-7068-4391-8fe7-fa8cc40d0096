import React, { useContext } from 'react';
import { assets } from '../../assets/assets';
import { AppContext } from '../../Context/AppContext';
import { NavLink } from 'react-router-dom';

export const Sidebar = () => {
  const { isEducator } = useContext(AppContext);

  const menuItems = [
    {
      name: 'Dashboard',
      path: '/educator',
      icon: assets.home_icon,
      description: 'Overview & Analytics'
    },
    {
      name: 'Add Course',
      path: '/educator/add-course',
      icon: assets.add_icon,
      description: 'Create New Course'
    },
    {
      name: 'My Courses',
      path: '/educator/my-courses',
      icon: assets.my_course_icon,
      description: 'Manage Courses'
    },
    {
      name: 'Students',
      path: '/educator/student-enrolled',
      icon: assets.person_tick_icon,
      description: 'Enrolled Students'
    },
  ];

  return isEducator && (
    <aside className="md:w-72 w-20 border-r border-gray-200/50 min-h-screen bg-gradient-to-b from-white to-gray-50/50 shadow-soft backdrop-blur-sm">

      {/* Sidebar Header */}
      <div className="p-6 border-b border-gray-200/50">
        <div className="hidden md:block">
          <h2 className="text-lg font-semibold text-gray-800 font-secondary">Educator Panel</h2>
          <p className="text-sm text-gray-500 mt-1">Manage your courses and students</p>
        </div>
        <div className="md:hidden flex justify-center">
          <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
            <div className="w-4 h-4 bg-primary-600 rounded-sm"></div>
          </div>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="py-6">
        <div className="space-y-2 px-4">
          {menuItems.map((item) => (
            <NavLink
              to={item.path}
              key={item.name}
              end={item.path === '/educator'}
              className={({ isActive }) =>
                `group relative flex items-center md:flex-row flex-col md:justify-start justify-center py-4 md:px-4 px-2 rounded-xl transition-all duration-300 ${
                  isActive
                    ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-colored transform scale-105'
                    : 'text-gray-600 hover:bg-white hover:text-primary-600 hover:shadow-soft hover:scale-105'
                }`
              }
            >
              {({ isActive }) => (
                <>
                  {/* Icon */}
                  <div className={`flex items-center justify-center w-10 h-10 rounded-lg transition-all duration-300 ${
                    isActive
                      ? 'bg-white/20'
                      : 'bg-gray-100 group-hover:bg-primary-100'
                  }`}>
                    <img
                      src={item.icon}
                      alt={item.name}
                      className={`w-5 h-5 transition-all duration-300 ${
                        isActive ? 'filter brightness-0 invert' : 'group-hover:filter group-hover:brightness-0 group-hover:invert-0'
                      }`}
                    />
                  </div>

                  {/* Text Content */}
                  <div className="md:block hidden ml-4 flex-1">
                    <p className={`font-semibold text-sm transition-colors duration-300 ${
                      isActive ? 'text-white' : 'text-gray-800 group-hover:text-primary-600'
                    }`}>
                      {item.name}
                    </p>
                    <p className={`text-xs mt-0.5 transition-colors duration-300 ${
                      isActive ? 'text-white/80' : 'text-gray-500 group-hover:text-primary-500'
                    }`}>
                      {item.description}
                    </p>
                  </div>

                  {/* Active Indicator */}
                  {isActive && (
                    <div className="hidden md:block absolute right-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-white rounded-l-full"></div>
                  )}

                  {/* Mobile Tooltip */}
                  <div className="md:hidden absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap z-50">
                    {item.name}
                  </div>
                </>
              )}
            </NavLink>
          ))}
        </div>
      </nav>

      {/* Bottom Section */}
      <div className="absolute bottom-6 left-4 right-4">
        <div className="hidden md:block p-4 bg-gradient-to-r from-primary-50 to-secondary-50 rounded-xl border border-primary-200/50">
          <h3 className="font-semibold text-sm text-gray-800 mb-1">Need Help?</h3>
          <p className="text-xs text-gray-600 mb-3">Check our documentation</p>
          <button className="w-full bg-white text-primary-600 text-xs font-semibold py-2 rounded-lg hover:bg-primary-50 transition-colors duration-200">
            View Docs
          </button>
        </div>

        <div className="md:hidden flex justify-center">
          <button className="w-10 h-10 bg-primary-100 hover:bg-primary-200 rounded-lg flex items-center justify-center transition-colors duration-200">
            <span className="text-primary-600 text-lg">?</span>
          </button>
        </div>
      </div>
    </aside>
  );
};