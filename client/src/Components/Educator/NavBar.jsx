import React from 'react'
import { assets, dummyEducatorData } from '../../assets/assets'
import { UserButton, useUser } from '@clerk/clerk-react'
import { Link } from 'react-router-dom'

export const NavBar = () => {
  const educatorData = dummyEducatorData
  const { user } = useUser()

  return (
    <nav className='sticky top-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-200/50 shadow-soft'>
      <div className='flex items-center justify-between px-4 md:px-8 py-4'>

        {/* Logo */}
        <Link to="/" className="flex items-center space-x-3 group">
            <div className="relative logo-glow">
              <div className="w-12 h-12 bg-gradient-to-br from-indigo-600 via-purple-600 to-cyan-600 rounded-2xl flex items-center justify-center transform group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-lg group-hover:shadow-xl">
                <span className="text-white font-bold text-xl">S</span>
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full animate-pulse shadow-lg"></div>
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-indigo-400 to-purple-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
            </div>
            <div className="flex flex-col">
              <span className="text-2xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 bg-clip-text text-transparent group-hover:from-purple-600 group-hover:to-indigo-600 transition-all duration-300">
                Skill Matrix
              </span>
              <span className="text-xs text-gray-500 font-medium -mt-1 group-hover:text-indigo-500 transition-colors duration-300">Learn. Grow. Excel.</span>
            </div>
          </Link>

        {/* User Info and Actions */}
        <div className='flex items-center gap-6'>

          {/* Welcome Message */}
          <div className='hidden md:flex items-center gap-4'>
            <div className='text-right'>
              <p className='text-sm text-gray-500 font-medium'>Welcome back,</p>
              <p className='text-lg font-semibold text-gray-800 font-secondary'>
                {user ? user.fullName : 'Educator'}
              </p>
            </div>

            {/* Status Badge */}
            <div className='flex items-center gap-2 px-3 py-1.5 bg-success-50 border border-success-200 rounded-full'>
              <div className='w-2 h-2 bg-success-500 rounded-full animate-pulse'></div>
              <span className='text-sm font-medium text-success-700'>Online</span>
            </div>
          </div>

          {/* Divider */}
          <div className='hidden md:block w-px h-8 bg-gray-300'></div>

          {/* User Avatar */}
          <div className='flex items-center'>
            {user ? (
              <UserButton
                appearance={{
                  elements: {
                    avatarBox: "w-12 h-12 rounded-full ring-2 ring-primary-200 hover:ring-primary-300 transition-all duration-200 shadow-soft hover:shadow-medium"
                  }
                }}
              />
            ) : (
              <div className='w-12 h-12 rounded-full overflow-hidden ring-2 ring-gray-200 shadow-soft'>
                <img
                  className='w-full h-full object-cover'
                  src={assets.profile_img}
                  alt='Profile'
                />
              </div>
            )}
          </div>

          {/* Mobile Welcome */}
          <div className='md:hidden flex flex-col items-end'>
            <p className='text-sm font-semibold text-gray-800'>
              {user ? user.firstName || 'Educator' : 'Educator'}
            </p>
            <div className='flex items-center gap-1'>
              <div className='w-1.5 h-1.5 bg-success-500 rounded-full'></div>
              <span className='text-xs text-success-600'>Online</span>
            </div>
          </div>
        </div>
      </div>
    </nav>
  )
}
