import React, { useState } from 'react';
import { assets } from '../../assets/assets';
import { useNavigate } from 'react-router-dom';

export const SearchBar = ({ data }) => {
  const navigate = useNavigate();
  const [input, setInput] = useState(data ? data : '');

  const onSearchHandler = (e) => {
    e.preventDefault();
    navigate('/course-list/' + input);
  };

  return (
    <form onSubmit={onSearchHandler} className='max-w-2xl w-full group'>
      <div className='relative flex items-center bg-white/90 backdrop-blur-sm border-2 border-gray-200 rounded-2xl shadow-lg hover:shadow-xl focus-within:shadow-xl focus-within:border-indigo-300 transition-all duration-300 overflow-hidden'>
        {/* Search icon */}
        <div className='flex items-center justify-center pl-6 pr-3'>
          <div className='w-5 h-5 text-gray-400 group-focus-within:text-indigo-500 transition-colors duration-300'>
            <img
              src={assets.search_icon}
              alt='search_icon'
              className='w-5 h-5 opacity-60 group-focus-within:opacity-100 transition-opacity duration-300'
            />
          </div>
        </div>

        {/* Input field */}
        <input
          onChange={(e) => setInput(e.target.value)}
          value={input}
          type='text'
          placeholder='Search for courses, topics, or instructors...'
          className='flex-1 h-16 outline-none text-gray-700 placeholder-gray-400 bg-transparent font-medium text-lg px-2 focus:placeholder-gray-300 transition-colors duration-300'
        />

        {/* Search button */}
        <button
          type='submit'
          className='bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold px-8 py-4 m-2 rounded-xl hover:shadow-lg transform hover:scale-105 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-indigo-200 active:scale-95'
        >
          <span className='hidden sm:inline'>Search</span>
          <span className='sm:hidden'>Go</span>
        </button>
      </div>

      {/* Popular searches */}
      <div className='mt-4 flex flex-wrap gap-2 justify-center'>
        <span className='text-sm text-gray-500 mr-2 font-medium'>Popular:</span>
        {[
          { term: 'React', color: 'hover:bg-blue-50 hover:text-blue-700 hover:border-blue-200' },
          { term: 'Python', color: 'hover:bg-green-50 hover:text-green-700 hover:border-green-200' },
          { term: 'Design', color: 'hover:bg-purple-50 hover:text-purple-700 hover:border-purple-200' },
          { term: 'Business', color: 'hover:bg-orange-50 hover:text-orange-700 hover:border-orange-200' }
        ].map((item) => (
          <button
            key={item.term}
            type='button'
            onClick={() => {
              setInput(item.term);
              navigate('/course-list/' + item.term);
            }}
            className={`px-3 py-1.5 text-sm bg-gray-100 text-gray-600 rounded-full border border-gray-200 transition-all duration-200 ${item.color}`}
          >
            {item.term}
          </button>
        ))}
      </div>
    </form>
  );
};