import React from 'react'
import { assets } from '../../assets/assets'
import { useContext } from 'react'
import { AppContext } from '../../Context/AppContext'
import { Link } from 'react-router-dom'



export const CourseCard = ({course}) => {
    const {currency, calculateRating} = useContext(AppContext)
    const rating = calculateRating(course)
    const discountedPrice = (course.coursePrice - course.discount * course.coursePrice / 100).toFixed(2)
    const originalPrice = course.coursePrice.toFixed(2)
    const hasDiscount = course.discount > 0

    return (
        <Link
            to={'/course/' + course._id}
            onClick={() => scrollTo(0, 0)}
            className='group block bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl border border-gray-100 hover:border-indigo-200 transition-all duration-300 hover:-translate-y-2'
        >
            {/* Course Thumbnail */}
            <div className='relative overflow-hidden'>
                <img
                    className='w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300'
                    src={course?.courseThumbnail}
                    alt={course?.courseTitle}
                />

                {/* Discount Badge */}
                {hasDiscount && (
                    <div className='absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold shadow-lg'>
                        {course.discount}% OFF
                    </div>
                )}

                {/* Category Badge */}
                <div className='absolute top-4 right-4 bg-indigo-500 text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg'>
                    {course.category || 'Course'}
                </div>

                {/* Subtle Overlay */}
                <div className='absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300'></div>
            </div>

            {/* Course Content */}
            <div className='p-6'>
                {/* Course Title */}
                <h3 className='text-lg font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-indigo-600 transition-colors duration-200 font-secondary'>
                    {course?.courseTitle}
                </h3>

                {/* Instructor */}
                <div className='flex items-center gap-2 mb-3'>
                    <div className='w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-semibold shadow-md'>
                        {course.educator?.name?.charAt(0) || 'I'}
                    </div>
                    <p className='text-gray-600 text-sm font-medium'>{course.educator?.name}</p>
                </div>

                {/* Rating */}
                <div className='flex items-center gap-3 mb-4'>
                    <div className='flex items-center gap-1'>
                        <span className='text-sm font-semibold text-gray-800'>{rating}</span>
                        <div className='flex'>
                            {[...Array(5)].map((_, i) => (
                                <img
                                    className='w-4 h-4'
                                    key={i}
                                    src={i < Math.floor(rating) ? assets.star : assets.star_blank}
                                    alt='star'
                                />
                            ))}
                        </div>
                    </div>
                    <span className='text-gray-400'>•</span>
                    <p className='text-sm text-gray-500'>
                        ({course.courseRatings.length} {course.courseRatings.length === 1 ? 'review' : 'reviews'})
                    </p>
                </div>

                {/* Course Stats */}
                <div className='flex items-center gap-4 mb-4 text-sm text-gray-500'>
                    <div className='flex items-center gap-1'>
                        <svg className='w-4 h-4' fill='currentColor' viewBox='0 0 20 20'>
                            <path d='M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z'/>
                        </svg>
                        <span>{course.enrolledStudents?.length || 0} students</span>
                    </div>
                    <span>•</span>
                    <div className='flex items-center gap-1'>
                        <svg className='w-4 h-4' fill='currentColor' viewBox='0 0 20 20'>
                            <path fillRule='evenodd' d='M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z' clipRule='evenodd'/>
                        </svg>
                        <span>{course.lessons?.length || 0} lessons</span>
                    </div>
                </div>

                {/* Price */}
                <div className='flex items-center justify-between'>
                    <div className='flex items-center gap-2'>
                        <p className='text-xl font-bold text-gray-900'>
                            {currency}{discountedPrice}
                        </p>
                        {hasDiscount && (
                            <p className='text-sm text-gray-500 line-through'>
                                {currency}{originalPrice}
                            </p>
                        )}
                    </div>

                    {/* Enroll Button */}
                    <button className='bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-4 py-2 rounded-lg font-semibold text-sm opacity-0 group-hover:opacity-100 transform translate-x-4 group-hover:translate-x-0 transition-all duration-300 hover:shadow-lg'>
                        Enroll Now
                    </button>
                </div>
            </div>
        </Link>
    )
}
