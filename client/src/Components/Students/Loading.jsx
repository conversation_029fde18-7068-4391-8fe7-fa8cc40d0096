import React, { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

export const Loading = () => {
  const {path} = useParams()
  const navigate = useNavigate()

  useEffect(() => {
    if(path) {
      const timer = setTimeout(() => {
        navigate(`/${path}`)
      }, 5000)
      return () => clearTimeout(timer)
    }
  }, [path, navigate])

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      {/* Loading Header */}
      <div className="text-center pt-20 pb-12">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-primary rounded-2xl mb-6 animate-pulse-soft">
          <div className="w-8 h-8 border-3 border-white border-t-transparent rounded-full animate-spin"></div>
        </div>
        <h2 className="text-2xl font-semibold text-gray-800 mb-2 font-secondary">Loading Amazing Content</h2>
        <p className="text-gray-600">Please wait while we prepare everything for you...</p>
      </div>

      {/* Course Cards Skeleton */}
      <div className="px-8 md:px-36">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {[...Array(8)].map((_, index) => (
            <div
              key={index}
              className="bg-white rounded-2xl overflow-hidden shadow-soft animate-pulse"
              style={{animationDelay: `${index * 0.1}s`}}
            >
              {/* Image Skeleton */}
              <div className="h-48 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-pulse"></div>

              {/* Content Skeleton */}
              <div className="p-6 space-y-4">
                {/* Title */}
                <div className="space-y-2">
                  <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded animate-pulse"></div>
                  <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-3/4 animate-pulse"></div>
                </div>

                {/* Instructor */}
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded-full animate-pulse"></div>
                  <div className="h-3 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-24 animate-pulse"></div>
                </div>

                {/* Rating */}
                <div className="flex items-center space-x-2">
                  <div className="h-3 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-12 animate-pulse"></div>
                  <div className="flex space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="w-4 h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded animate-pulse"></div>
                    ))}
                  </div>
                </div>

                {/* Stats */}
                <div className="flex items-center space-x-4">
                  <div className="h-3 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-16 animate-pulse"></div>
                  <div className="h-3 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-16 animate-pulse"></div>
                </div>

                {/* Price */}
                <div className="flex items-center justify-between">
                  <div className="h-6 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-20 animate-pulse"></div>
                  <div className="h-8 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-24 animate-pulse"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Progress Bar */}
      <div className="fixed bottom-8 left-1/2 transform -translate-x-1/2">
        <div className="bg-white/90 backdrop-blur-sm rounded-full px-6 py-3 shadow-large border border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-4 h-4 border-2 border-primary-600 border-t-transparent rounded-full animate-spin"></div>
            <span className="text-sm font-medium text-gray-700">Loading courses...</span>
          </div>
        </div>
      </div>
    </div>
  );
};

// <div
// >
    
// </div>