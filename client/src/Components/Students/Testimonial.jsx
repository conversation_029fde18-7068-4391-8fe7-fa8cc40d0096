import React from 'react';
import { assets, dummyTestimonial } from '../../assets/assets';
import { Link } from 'react-router-dom';

export const Testimonial = () => {
  return (
    <section className='py-20 px-8 md:px-40 bg-gradient-to-b from-gray-50 to-white'>
      <div className='max-w-7xl mx-auto'>
        {/* Section Header */}
        <div className='text-center mb-16'>
          <div className='inline-flex items-center px-4 py-2 bg-purple-100 border border-purple-200 rounded-full text-sm font-semibold text-purple-700 mb-6'>
            <span className='w-2 h-2 bg-purple-500 rounded-full mr-2'></span>
            Student Success Stories
          </div>

          <h2 className='text-4xl md:text-5xl font-bold text-gray-900 mb-6 font-secondary'>
            What our{' '}
            <span className='bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent'>
              students
            </span>{' '}
            say
          </h2>

          <p className='text-lg text-gray-600 leading-relaxed max-w-3xl mx-auto'>
            Hear from our learners as they share their journeys of transformation, success, and how our
            platform has made a difference in their lives.
          </p>
        </div>

        {/* Testimonials Grid */}
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'>
          {dummyTestimonial.map((testimonial, index) => (
            <div
              key={index}
              className='group bg-white rounded-2xl shadow-lg hover:shadow-xl border border-gray-100 hover:border-purple-200 transition-all duration-300 hover:-translate-y-2 overflow-hidden animate-slide-up'
              style={{animationDelay: `${index * 0.1}s`}}
            >
              {/* Header with user info */}
              <div className='flex items-center gap-4 p-6 bg-gradient-to-r from-gray-50 to-purple-50'>
                <div className='relative'>
                  <img
                    className='w-14 h-14 rounded-full object-cover ring-2 ring-white shadow-lg'
                    src={testimonial.image}
                    alt={testimonial.name}
                  />
                  <div className='absolute -bottom-1 -right-1 w-5 h-5 bg-emerald-500 rounded-full border-2 border-white flex items-center justify-center'>
                    <svg className='w-3 h-3 text-white' fill='currentColor' viewBox='0 0 20 20'>
                      <path fillRule='evenodd' d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z' clipRule='evenodd' />
                    </svg>
                  </div>
                </div>
                <div className='flex-1'>
                  <h3 className='text-lg font-semibold text-gray-900 group-hover:text-purple-600 transition-colors duration-300'>
                    {testimonial.name}
                  </h3>
                  <p className='text-gray-600 text-sm font-medium'>{testimonial.role}</p>
                </div>
              </div>

              {/* Content */}
              <div className='p-6'>
                {/* Rating */}
                <div className='flex items-center gap-1 mb-4'>
                  {[...Array(5)].map((_, i) => (
                    <img
                      className='w-5 h-5'
                      key={i}
                      src={i < Math.floor(testimonial.rating) ? assets.star : assets.star_blank}
                      alt='star'
                    />
                  ))}
                  <span className='ml-2 text-sm font-semibold text-gray-700'>
                    {testimonial.rating}
                  </span>
                </div>

                {/* Feedback */}
                <blockquote className='text-gray-600 leading-relaxed mb-4 relative'>
                  <svg className='absolute -top-2 -left-2 w-6 h-6 text-purple-200' fill='currentColor' viewBox='0 0 24 24'>
                    <path d='M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z'/>
                  </svg>
                  <span className='relative z-10'>{testimonial.feedback}</span>
                </blockquote>

                {/* Read More Link */}
                <Link
                  className='inline-flex items-center text-purple-600 hover:text-purple-700 font-medium text-sm group-hover:translate-x-1 transition-all duration-300'
                  to={`/testimonial/${testimonial.id}`}
                >
                  Read Full Story
                  <svg className='w-4 h-4 ml-1' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                    <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M17 8l4 4m0 0l-4 4m4-4H3' />
                  </svg>
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className='text-center mt-16'>
          <p className='text-gray-600 mb-6'>Ready to start your own success story?</p>
          <button className='bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-8 py-4 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300'>
            Join Our Community
          </button>
        </div>
      </div>
    </section>
  );
};