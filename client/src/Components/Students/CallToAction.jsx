import React from 'react';
import { assets } from '../../assets/assets';

export const CallToAction = () => {
  return (
    <section className='py-20 px-8 md:px-40'>
      <div className='max-w-6xl mx-auto'>
        {/* Main CTA Container */}
        <div className='relative overflow-hidden bg-gradient-to-br from-indigo-600 to-purple-700 rounded-3xl shadow-xl'>
          {/* Background Pattern */}
          <div className='absolute inset-0 bg-gradient-to-r from-black/10 to-transparent'></div>
          <div className='absolute top-0 right-0 w-96 h-96 bg-white/5 rounded-full blur-3xl transform translate-x-32 -translate-y-32'></div>
          <div className='absolute bottom-0 left-0 w-80 h-80 bg-white/5 rounded-full blur-3xl transform -translate-x-32 translate-y-32'></div>

          {/* Content */}
          <div className='relative z-10 text-center py-16 px-8 md:px-16'>
            {/* Badge */}
            <div className='inline-flex items-center px-4 py-2 bg-white/20 backdrop-blur-sm border border-white/30 rounded-full text-sm font-medium text-white mb-8'>
              <span className='w-2 h-2 bg-white rounded-full mr-2 animate-pulse'></span>
              Start Your Learning Journey Today
            </div>

            {/* Main Heading */}
            <h1 className='text-4xl md:text-5xl font-bold text-white mb-6 font-secondary leading-tight'>
              Learn anything,{' '}
              <span className='block'>
                anytime,{' '}
                <span className='text-cyan-300'>anywhere</span>
              </span>
            </h1>

            {/* Description */}
            <p className='text-lg text-white/90 mb-10 max-w-3xl mx-auto leading-relaxed'>
              Join thousands of learners who are already transforming their careers with our comprehensive courses.
              Start your journey to success today with expert-led content and hands-on projects.
            </p>

            {/* Action Buttons */}
            <div className='flex flex-col sm:flex-row justify-center items-center gap-4 mb-8'>
              <button className='group bg-white text-indigo-600 px-8 py-4 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-2'>
                <span>Get Started Free</span>
                <svg className='w-5 h-5 group-hover:translate-x-1 transition-transform duration-300' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                  <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M17 8l4 4m0 0l-4 4m4-4H3' />
                </svg>
              </button>

              <button className='group flex items-center px-8 py-4 text-white border-2 border-white/30 rounded-xl hover:bg-white/10 hover:border-white transition-all duration-300 backdrop-blur-sm'>
                <span className='mr-2'>Watch Demo</span>
                <div className='w-8 h-8 bg-white/20 rounded-full flex items-center justify-center group-hover:bg-white/30 transition-colors duration-300'>
                  <svg className='w-4 h-4 text-white ml-0.5' fill='currentColor' viewBox='0 0 24 24'>
                    <path d='M8 5v14l11-7z'/>
                  </svg>
                </div>
              </button>
            </div>

            {/* Trust Indicators */}
            <div className='flex flex-wrap justify-center items-center gap-8 text-white/80 text-sm'>
              <div className='flex items-center gap-2'>
                <svg className='w-5 h-5 text-yellow-300' fill='currentColor' viewBox='0 0 20 20'>
                  <path d='M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z' />
                </svg>
                <span>4.9/5 Rating</span>
              </div>
              <div className='flex items-center gap-2'>
                <svg className='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
                  <path d='M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z' />
                </svg>
                <span>50,000+ Students</span>
              </div>
              <div className='flex items-center gap-2'>
                <svg className='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
                  <path fillRule='evenodd' d='M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z' clipRule='evenodd' />
                </svg>
                <span>Certified Courses</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};