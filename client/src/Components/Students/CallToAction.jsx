import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, BadgeCheck, PlayCircle } from 'lucide-react';

export const CallToAction = () => {
  return (
    <section className='py-20 px-8 md:px-40'>
      <div className='max-w-6xl mx-auto'>
        <div className='relative overflow-hidden bg-gradient-to-br from-indigo-600 to-purple-700 rounded-3xl shadow-xl'>
          {/* Background */}
          <div className='absolute inset-0 bg-gradient-to-r from-black/10 to-transparent'></div>
          <div className='absolute top-0 right-0 w-96 h-96 bg-white/5 rounded-full blur-3xl transform translate-x-32 -translate-y-32'></div>
          <div className='absolute bottom-0 left-0 w-80 h-80 bg-white/5 rounded-full blur-3xl transform -translate-x-32 translate-y-32'></div>

          {/* Content */}
          <div className='relative z-10 text-center py-16 px-8 md:px-16'>
            {/* Badge */}
            <div className='inline-flex items-center px-4 py-2 bg-white/20 backdrop-blur-sm border border-white/30 rounded-full text-sm font-medium text-white mb-8'>
              <span className='w-2 h-2 bg-white rounded-full mr-2 animate-pulse'></span>
              Start Your Learning Journey Today
            </div>

            {/* Heading */}
            <h1 className='text-4xl md:text-5xl font-bold text-white mb-6 font-secondary leading-tight'>
              Learn anything,
              <span className='block'>
                anytime, <span className='text-cyan-300'>anywhere</span>
              </span>
            </h1>

            {/* Description */}
            <p className='text-lg text-white/90 mb-10 max-w-3xl mx-auto leading-relaxed'>
              Join thousands of learners who are already transforming their careers with our comprehensive courses.
              Start your journey to success today with expert-led content and hands-on projects.
            </p>

            {/* Buttons */}
            <div className='flex flex-col sm:flex-row justify-center items-center gap-4 mb-8'>
              <button className='group bg-white text-indigo-600 px-8 py-4 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-2'>
                <span>Get Started Free</span>
                <ArrowRight className='w-5 h-5 group-hover:translate-x-1 transition-transform duration-300' />
              </button>

              <button className='group flex items-center px-8 py-4 text-white border-2 border-white/30 rounded-xl hover:bg-white/10 hover:border-white transition-all duration-300 backdrop-blur-sm'>
                <span className='mr-2'>Watch Demo</span>
                <div className='w-8 h-8 bg-white/20 rounded-full flex items-center justify-center group-hover:bg-white/30 transition-colors duration-300'>
                  <PlayCircle className='w-5 h-5 text-white' />
                </div>
              </button>
            </div>

            {/* Trust Indicators */}
            <div className='flex flex-wrap justify-center items-center gap-8 text-white/80 text-sm'>
              <div className='flex items-center gap-2'>
                <Star className='w-5 h-5 text-yellow-300' />
                <span>4.9/5 Rating</span>
              </div>
              <div className='flex items-center gap-2'>
                <Users className='w-5 h-5' />
                <span>50,000+ Students</span>
              </div>
              <div className='flex items-center gap-2'>
                <BadgeCheck className='w-5 h-5' />
                <span>Certified Courses</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
