import React, { useContext } from "react";
import { assets } from "../../assets/assets";
import { Link } from "react-router-dom";
import { use<PERSON><PERSON><PERSON>,User<PERSON>utton,useUser } from "@clerk/clerk-react";
import { AppContext } from "../../Context/AppContext";
import axios from "axios";
import { toast } from "react-toastify";
export const Navbar = () => {



  const {navigate,isEducator,backendUrl,setIsEducator,getToken}=useContext(AppContext)

  const isCourseListPage = location.pathname.includes("/course-list");
  const {openSignIn}=useClerk()
  const {user}=useUser();

  const becomeEducator=async ()=>{
    try{
      if(isEducator){
        navigate('/educator')
        return;
      }
      const token=await getToken()
      const {data}=await axios.get(backendUrl+'/api/educator/update-role',
        {headers:{Authorization:`Bear<PERSON> ${token}`}}
      )
      if(data.success){
        setIsEducator(true)
        toast.success(data.message)
      }
      else{
        toast.error(data.message)
      }
    }catch(error){
      toast.error(error.message)
    }
  }

  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 navbar-glass navbar-shadow transition-all duration-500 ${
      isCourseListPage
        ? "bg-white/98"
        : "bg-white/95"
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-20">
          {/* Enhanced Logo */}
          <Link to="/" className="flex items-center space-x-3 group">
            <div className="relative logo-glow">
              <div className="w-12 h-12 bg-gradient-to-br from-indigo-600 via-purple-600 to-cyan-600 rounded-2xl flex items-center justify-center transform group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-lg group-hover:shadow-xl">
                <span className="text-white font-bold text-xl">S</span>
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full animate-pulse shadow-lg"></div>
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-indigo-400 to-purple-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
            </div>
            <div className="flex flex-col">
              <span className="text-2xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 bg-clip-text text-transparent group-hover:from-purple-600 group-hover:to-indigo-600 transition-all duration-300">
                Skill Matrix
              </span>
              <span className="text-xs text-gray-500 font-medium -mt-1 group-hover:text-indigo-500 transition-colors duration-300">Learn. Grow. Excel.</span>
            </div>
          </Link>

          {/* Enhanced Desktop Navigation */}
          <div className="hidden md:flex items-center gap-8">
            {user && (
              <div className="flex items-center gap-6">
                <button
                  onClick={becomeEducator}
                  className="relative px-4 py-2 text-gray-700 font-semibold hover:text-indigo-600 transition-all duration-300 group rounded-lg hover:bg-indigo-50 nav-link-hover"
                >
                  <div className="flex items-center gap-2">
                    <span className="text-indigo-500 group-hover:scale-110 transition-transform duration-300">
                      {isEducator ? '🎓' : '✨'}
                    </span>
                    {isEducator ? 'Educator Dashboard' : 'Become Educator'}
                  </div>
                  <span className="absolute -bottom-1 left-4 w-0 h-0.5 bg-gradient-to-r from-indigo-600 to-purple-600 transition-all duration-300 group-hover:w-[calc(100%-2rem)] rounded-full"></span>
                </button>

                <div className="w-px h-8 bg-gradient-to-b from-transparent via-gray-300 to-transparent"></div>

                <Link
                  to="/my-enrollments"
                  className="relative px-4 py-2 text-gray-700 font-semibold hover:text-indigo-600 transition-all duration-300 group rounded-lg hover:bg-indigo-50 nav-link-hover"
                >
                  <div className="flex items-center gap-2">
                    <span className="text-indigo-500 group-hover:scale-110 transition-transform duration-300">📚</span>
                    My Enrollments
                  </div>
                  <span className="absolute -bottom-1 left-4 w-0 h-0.5 bg-gradient-to-r from-indigo-600 to-purple-600 transition-all duration-300 group-hover:w-[calc(100%-2rem)] rounded-full"></span>
                </Link>
              </div>
            )}

            {/* Enhanced Auth Section */}
            <div className="flex items-center">
              {user ? (
                <div className="flex items-center gap-4">
                  <div className="w-px h-8 bg-gradient-to-b from-transparent via-gray-300 to-transparent"></div>
                  <div className="flex items-center gap-3 bg-gradient-to-r from-indigo-50 to-purple-50 px-3 py-2 rounded-full border border-indigo-200">
                    <UserButton
                      appearance={{
                        elements: {
                          avatarBox: "w-10 h-10 rounded-full ring-2 ring-indigo-300 hover:ring-indigo-400 transition-all duration-300 shadow-lg"
                        }
                      }}
                    />
                    <div className="flex flex-col">
                      <span className="text-sm font-semibold text-gray-800">{user.name || 'User'}</span>
                      <span className="text-xs text-gray-500">Student</span>
                    </div>
                  </div>
                </div>
              ) : (
                <button
                  onClick={() => openSignIn()}
                  className="relative bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 text-white px-8 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-indigo-200 overflow-hidden group"
                >
                  <span className="relative z-10 flex items-center gap-2">
                    <span>✨</span>
                    Get Started
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-cyan-600 via-purple-600 to-indigo-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>
              )}
            </div>
          </div>

          {/* Enhanced Mobile Navigation */}
          <div className="md:hidden flex items-center gap-3">
            {user && (
              <div className="flex items-center gap-2">
                <button
                  onClick={becomeEducator}
                  className="bg-indigo-100 text-indigo-700 px-3 py-1.5 rounded-lg font-semibold hover:bg-indigo-200 transition-all duration-200 text-sm"
                >
                  {isEducator ? '🎓 Dashboard' : '✨ Educator'}
                </button>

                <Link
                  to="/my-enrollments"
                  className="bg-purple-100 text-purple-700 px-3 py-1.5 rounded-lg font-semibold hover:bg-purple-200 transition-all duration-200 text-sm"
                >
                  📚 Courses
                </Link>
              </div>
            )}

            {/* Enhanced Mobile Auth */}
            {user ? (
              <UserButton
                appearance={{
                  elements: {
                    avatarBox: "w-9 h-9 rounded-full ring-2 ring-indigo-300 hover:ring-indigo-400 transition-all duration-300 shadow-md"
                  }
                }}
              />
            ) : (
              <button
                onClick={() => openSignIn()}
                className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-4 py-2.5 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 text-sm"
              >
                <span className="flex items-center gap-1">
                  <span>✨</span>
                  Start
                </span>
              </button>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};
