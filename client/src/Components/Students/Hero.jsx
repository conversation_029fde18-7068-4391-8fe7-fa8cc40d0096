import React from 'react'
import { assets } from '../../assets/assets'
import { SearchBar } from './SearchBar'

export const Hero = () => {
  return (
    <div className='relative overflow-hidden'>
      {/* Elegant gradient background */}
      <div className='absolute inset-0 bg-gradient-to-br from-indigo-50 via-white to-cyan-50'></div>
      <div className='absolute inset-0 bg-gradient-to-t from-white/50 via-transparent to-transparent'></div>

      {/* Subtle floating elements */}
      <div className='absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full blur-3xl animate-pulse-soft opacity-60'></div>
      <div className='absolute bottom-20 right-10 w-80 h-80 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full blur-3xl animate-pulse-soft opacity-50' style={{animationDelay: '2s'}}></div>
      <div className='absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-128 h-128 bg-gradient-to-r from-cyan-50 to-blue-50 rounded-full blur-3xl opacity-30'></div>

      {/* Geometric shapes for visual interest */}
      <div className='absolute top-40 right-1/3 w-4 h-4 bg-indigo-400 rounded-full animate-bounce opacity-40' style={{animationDelay: '1s'}}></div>
      <div className='absolute bottom-40 left-1/3 w-3 h-3 bg-purple-400 rounded-full animate-bounce opacity-40' style={{animationDelay: '3s'}}></div>

      {/* Main content */}
      <div className='relative flex flex-col items-center justify-center w-full md:pt-40 pt-24 pb-20 px-7 md:px-0 space-y-8 text-center min-h-[80vh]'>

        {/* Elegant Badge */}
        <div className='inline-flex items-center px-6 py-3 bg-white/80 backdrop-blur-sm border border-indigo-200 rounded-full text-sm font-semibold text-indigo-700 shadow-lg animate-slide-down hover:shadow-xl transition-all duration-300'>
          <span className='w-2 h-2 bg-indigo-500 rounded-full mr-3 animate-pulse'></span>
          ✨ Join 50,000+ learners worldwide
        </div>

        {/* Elegant main heading */}
        <div className='relative max-w-5xl mx-auto animate-slide-up'>
          <h1 className='md:text-6xl text-4xl font-bold text-gray-900 leading-tight mb-6 font-secondary'>
            Empower your future with
            <span className='block mt-2'>
              courses designed to{' '}
              <span className='bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 bg-clip-text text-transparent relative inline-block'>
                fit your choice
                <img
                  src={assets.sketch}
                  alt="sketch"
                  className='md:block hidden absolute -bottom-4 -right-8 w-24 h-auto opacity-60'
                />
              </span>
            </span>
          </h1>
        </div>

        {/* Elegant description */}
        <div className='max-w-3xl mx-auto space-y-6 animate-slide-up' style={{animationDelay: '0.2s'}}>
          <p className='md:block hidden text-xl text-gray-600 leading-relaxed font-medium'>
            We bring together world-class instructors, interactive content, and a supportive
            community to help you achieve your personal and professional goals.
          </p>
          <p className='md:hidden text-lg text-gray-600 leading-relaxed max-w-sm mx-auto'>
            World-class instructors and interactive content to help you achieve your goals.
          </p>

          {/* Elegant Stats */}
          <div className='flex flex-wrap justify-center items-center gap-6 mt-8'>
            <div className='flex items-center gap-3 bg-white/70 backdrop-blur-sm px-5 py-3 rounded-full shadow-md hover:shadow-lg transition-all duration-300'>
              <div className='w-2 h-2 bg-emerald-500 rounded-full'></div>
              <span className='text-sm font-semibold text-gray-700'>1000+ Courses</span>
            </div>
            <div className='flex items-center gap-3 bg-white/70 backdrop-blur-sm px-5 py-3 rounded-full shadow-md hover:shadow-lg transition-all duration-300'>
              <div className='w-2 h-2 bg-indigo-500 rounded-full'></div>
              <span className='text-sm font-semibold text-gray-700'>Expert Instructors</span>
            </div>
            <div className='flex items-center gap-3 bg-white/70 backdrop-blur-sm px-5 py-3 rounded-full shadow-md hover:shadow-lg transition-all duration-300'>
              <div className='w-2 h-2 bg-purple-500 rounded-full'></div>
              <span className='text-sm font-semibold text-gray-700'>Lifetime Access</span>
            </div>
          </div>
        </div>

        {/* Enhanced search bar */}
        <div className='w-full max-w-2xl mx-auto animate-slide-up' style={{animationDelay: '0.4s'}}>
          <SearchBar/>
        </div>

        {/* Elegant call to action buttons */}
        <div className='flex flex-col sm:flex-row gap-4 items-center justify-center mt-10 animate-slide-up' style={{animationDelay: '0.4s'}}>
          <button className='bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 hover:-translate-y-1'>
            Start Learning Today
          </button>
          <button className='bg-white/90 backdrop-blur-sm text-gray-700 px-8 py-4 rounded-xl font-semibold border border-gray-200 hover:bg-gray-50 hover:shadow-lg transition-all duration-300 hover:-translate-y-1'>
            Browse Courses
          </button>
        </div>

        {/* Scroll indicator */}
        <div className='absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce-gentle'>
          <div className='w-6 h-10 border-2 border-gray-300 rounded-full flex justify-center'>
            <div className='w-1 h-3 bg-gray-400 rounded-full mt-2 animate-pulse'></div>
          </div>
        </div>
      </div>
    </div>
  )
}
