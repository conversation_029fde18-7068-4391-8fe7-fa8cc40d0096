import React, { useContext } from 'react'
import { Link } from 'react-router-dom'
import { AppContext } from '../../Context/AppContext'
import { CourseCard } from './CourseCard'

export const CourseSection = () => {
    const {allCourses} = useContext(AppContext)

    return (
        <section className='py-20 md:px-40 px-8 bg-gradient-to-br from-slate-50 to-indigo-50 relative overflow-hidden'>
            {/* Subtle background elements */}
            <div className='absolute top-0 left-0 w-96 h-96 bg-gradient-to-r from-indigo-100 to-purple-100 rounded-full blur-3xl opacity-40'></div>
            <div className='absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-r from-cyan-100 to-blue-100 rounded-full blur-3xl opacity-40'></div>

            {/* Section Header */}
            <div className='relative text-center max-w-4xl mx-auto mb-16'>
                <div className='inline-flex items-center px-4 py-2 bg-indigo-100 border border-indigo-200 rounded-full text-sm font-semibold text-indigo-700 mb-6'>
                    <span className='w-2 h-2 bg-indigo-500 rounded-full mr-2'></span>
                    Featured Courses
                </div>

                <h2 className='text-4xl md:text-5xl font-bold text-gray-900 mb-6 font-secondary'>
                    Learn from the{' '}
                    <span className='bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent'>
                        best
                    </span>
                </h2>

                <p className='text-lg text-gray-600 leading-relaxed max-w-2xl mx-auto'>
                    Discover our top-rated courses across various categories. From coding and design to business and wellness, our courses are crafted by industry experts to deliver real results.
                </p>
            </div>

            {/* Course Grid */}
            <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mb-16'>
                {allCourses.slice(0, 4).map((course, index) => (
                    <div
                        key={index}
                        className='animate-slide-up'
                        style={{animationDelay: `${index * 0.1}s`}}
                    >
                        <CourseCard course={course} />
                    </div>
                ))}
            </div>

            {/* Show All Courses Button */}
            <div className='text-center'>
                <Link
                    to='/course-list'
                    onClick={() => scrollTo(0, 0)}
                    className='group inline-flex items-center gap-3 bg-white text-gray-700 border-2 border-gray-200 px-8 py-4 rounded-xl font-semibold hover:border-indigo-300 hover:text-indigo-600 hover:bg-indigo-50 transition-all duration-300 shadow-lg hover:shadow-xl'
                >
                    <span>Explore All Courses</span>
                    <svg
                        className='w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-300'
                        fill='none'
                        stroke='currentColor'
                        viewBox='0 0 24 24'
                    >
                        <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M17 8l4 4m0 0l-4 4m4-4H3' />
                    </svg>
                </Link>
            </div>

            {/* Stats Section */}
            <div className='mt-20 grid grid-cols-2 md:grid-cols-4 gap-8 text-center'>
                <div className='group bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1'>
                    <div className='text-3xl font-bold text-indigo-600 mb-2 group-hover:scale-110 transition-transform duration-300'>
                        {allCourses.length}+
                    </div>
                    <div className='text-gray-600 font-medium'>Courses Available</div>
                </div>
                <div className='group bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1'>
                    <div className='text-3xl font-bold text-emerald-600 mb-2 group-hover:scale-110 transition-transform duration-300'>
                        50K+
                    </div>
                    <div className='text-gray-600 font-medium'>Happy Students</div>
                </div>
                <div className='group bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1'>
                    <div className='text-3xl font-bold text-cyan-600 mb-2 group-hover:scale-110 transition-transform duration-300'>
                        100+
                    </div>
                    <div className='text-gray-600 font-medium'>Expert Instructors</div>
                </div>
                <div className='group bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1'>
                    <div className='text-3xl font-bold text-purple-600 mb-2 group-hover:scale-110 transition-transform duration-300'>
                        95%
                    </div>
                    <div className='text-gray-600 font-medium'>Success Rate</div>
                </div>
            </div>
        </section>
    )
}
