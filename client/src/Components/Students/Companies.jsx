import React from 'react'
import { assets } from '../../assets/assets'

export const Companies = () => {
  const companies = [
    { name: 'Microsoft', logo: assets.microsoft_logo },
    { name: 'Walmart', logo: assets.walmart_logo },
    { name: 'Accenture', logo: assets.accenture_logo },
    { name: 'Adobe', logo: assets.adobe_logo },
    { name: 'PayPal', logo: assets.paypal_logo }
  ]

  return (
    <section className='py-16 px-8 bg-white'>
      <div className='max-w-6xl mx-auto text-center'>
        {/* Header */}
        <div className='mb-12'>
          <p className='text-gray-600 font-medium mb-2'>Trusted by learners from</p>
          <h3 className='text-2xl font-semibold text-gray-800 font-secondary'>
            Leading companies worldwide
          </h3>
        </div>

        {/* Company Logos */}
        <div className='relative overflow-hidden'>
          {/* Gradient overlays for fade effect */}
          <div className='absolute left-0 top-0 bottom-0 w-20 bg-gradient-to-r from-white to-transparent z-10'></div>
          <div className='absolute right-0 top-0 bottom-0 w-20 bg-gradient-to-l from-white to-transparent z-10'></div>

          {/* Logo container with animation */}
          <div className='flex items-center justify-center gap-8 md:gap-16 animate-slide-up'>
            {companies.map((company, index) => (
              <div
                key={company.name}
                className='group flex-shrink-0 p-4 rounded-xl hover:bg-gray-50 transition-all duration-300 hover:scale-110'
                style={{animationDelay: `${index * 0.1}s`}}
              >
                <img
                  src={company.logo}
                  alt={`${company.name} logo`}
                  className='w-20 md:w-28 h-auto opacity-60 group-hover:opacity-100 transition-opacity duration-300 filter grayscale group-hover:grayscale-0'
                />
              </div>
            ))}
          </div>
        </div>

        {/* Stats */}
        <div className='mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center'>
          <div className='group'>
            <div className='text-2xl font-bold text-indigo-600 mb-1 group-hover:scale-110 transition-transform duration-300'>
              500+
            </div>
            <div className='text-sm text-gray-600'>Companies</div>
          </div>
          <div className='group'>
            <div className='text-2xl font-bold text-emerald-600 mb-1 group-hover:scale-110 transition-transform duration-300'>
              50K+
            </div>
            <div className='text-sm text-gray-600'>Professionals</div>
          </div>
          <div className='group'>
            <div className='text-2xl font-bold text-cyan-600 mb-1 group-hover:scale-110 transition-transform duration-300'>
              120+
            </div>
            <div className='text-sm text-gray-600'>Countries</div>
          </div>
          <div className='group'>
            <div className='text-2xl font-bold text-purple-600 mb-1 group-hover:scale-110 transition-transform duration-300'>
              98%
            </div>
            <div className='text-sm text-gray-600'>Satisfaction</div>
          </div>
        </div>
      </div>
    </section>
  )
}
