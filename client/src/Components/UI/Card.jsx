import React from 'react'

export const Card = ({ 
  children, 
  className = '', 
  variant = 'default',
  padding = 'md',
  hover = false,
  ...props 
}) => {
  const baseClasses = 'bg-white rounded-2xl border transition-all duration-300'
  
  const variants = {
    default: 'border-gray-100 shadow-soft',
    elevated: 'border-gray-100 shadow-medium',
    outlined: 'border-gray-200 shadow-none',
    glass: 'glass border-white/20 shadow-large',
    gradient: 'bg-gradient-to-br from-white to-gray-50/50 border-gray-100 shadow-soft'
  }
  
  const paddings = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-10'
  }
  
  const hoverClasses = hover ? 'hover:shadow-large hover:border-primary-200 hover-lift cursor-pointer' : ''
  
  const classes = `${baseClasses} ${variants[variant]} ${paddings[padding]} ${hoverClasses} ${className}`
  
  return (
    <div className={classes} {...props}>
      {children}
    </div>
  )
}

export const CardHeader = ({ children, className = '' }) => {
  return (
    <div className={`border-b border-gray-100 pb-4 mb-6 ${className}`}>
      {children}
    </div>
  )
}

export const CardTitle = ({ children, className = '' }) => {
  return (
    <h3 className={`text-xl font-semibold text-gray-900 font-secondary ${className}`}>
      {children}
    </h3>
  )
}

export const CardDescription = ({ children, className = '' }) => {
  return (
    <p className={`text-gray-600 mt-1 ${className}`}>
      {children}
    </p>
  )
}

export const CardContent = ({ children, className = '' }) => {
  return (
    <div className={className}>
      {children}
    </div>
  )
}

export const CardFooter = ({ children, className = '' }) => {
  return (
    <div className={`border-t border-gray-100 pt-4 mt-6 ${className}`}>
      {children}
    </div>
  )
}

// Stats Card component
export const StatsCard = ({ 
  title, 
  value, 
  description, 
  icon, 
  trend, 
  trendValue,
  color = 'primary',
  className = '' 
}) => {
  const colors = {
    primary: {
      bg: 'from-primary-500 to-primary-600',
      text: 'text-primary-600',
      ring: 'ring-primary-200'
    },
    secondary: {
      bg: 'from-secondary-500 to-secondary-600',
      text: 'text-secondary-600',
      ring: 'ring-secondary-200'
    },
    success: {
      bg: 'from-success-500 to-success-600',
      text: 'text-success-600',
      ring: 'ring-success-200'
    },
    warning: {
      bg: 'from-warning-500 to-warning-600',
      text: 'text-warning-600',
      ring: 'ring-warning-200'
    },
    error: {
      bg: 'from-error-500 to-error-600',
      text: 'text-error-600',
      ring: 'ring-error-200'
    }
  }
  
  const trendColors = {
    up: 'text-success-600 bg-success-50',
    down: 'text-error-600 bg-error-50',
    neutral: 'text-gray-600 bg-gray-50'
  }
  
  return (
    <Card hover className={`group ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className={`w-12 h-12 bg-gradient-to-r ${colors[color].bg} rounded-xl flex items-center justify-center shadow-colored`}>
          {React.cloneElement(icon, { 
            className: "w-6 h-6 text-white" 
          })}
        </div>
        {trend && trendValue && (
          <div className={`px-2 py-1 rounded-full text-xs font-semibold ${trendColors[trend]}`}>
            {trend === 'up' ? '↗' : trend === 'down' ? '↘' : '→'} {trendValue}
          </div>
        )}
      </div>
      
      <div className="space-y-1">
        <p className={`text-3xl font-bold text-gray-900 group-hover:${colors[color].text} transition-colors duration-300`}>
          {value}
        </p>
        <p className="text-gray-600 font-medium">{title}</p>
        {description && (
          <p className="text-sm text-gray-500">{description}</p>
        )}
      </div>
    </Card>
  )
}

// Feature Card component
export const FeatureCard = ({ 
  title, 
  description, 
  icon, 
  className = '',
  onClick 
}) => {
  return (
    <Card 
      hover 
      className={`text-center group ${className}`}
      onClick={onClick}
    >
      <div className="w-16 h-16 bg-gradient-to-r from-primary-100 to-secondary-100 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
        {React.cloneElement(icon, { 
          className: "w-8 h-8 text-primary-600" 
        })}
      </div>
      
      <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors duration-300">
        {title}
      </h3>
      
      <p className="text-gray-600 text-sm leading-relaxed">
        {description}
      </p>
    </Card>
  )
}
