import React from 'react'

export const Button = ({ 
  children, 
  variant = 'primary', 
  size = 'md', 
  className = '', 
  disabled = false,
  loading = false,
  icon = null,
  iconPosition = 'left',
  onClick,
  type = 'button',
  ...props 
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-xl transition-all duration-300 focus:outline-none focus:ring-4 disabled:opacity-50 disabled:cursor-not-allowed'
  
  const variants = {
    primary: 'bg-gradient-primary text-white shadow-colored hover:shadow-colored-lg hover:scale-105 focus:ring-primary-200 active:scale-95',
    secondary: 'bg-gradient-secondary text-white shadow-colored hover:shadow-colored-lg hover:scale-105 focus:ring-secondary-200 active:scale-95',
    outline: 'bg-white text-gray-700 border-2 border-gray-200 hover:border-primary-300 hover:text-primary-600 hover:bg-primary-50 shadow-soft hover:shadow-medium focus:ring-primary-200',
    ghost: 'text-gray-600 hover:text-primary-600 hover:bg-primary-50 focus:ring-primary-200',
    danger: 'bg-gradient-to-r from-error-500 to-error-600 text-white shadow-colored hover:shadow-colored-lg hover:scale-105 focus:ring-error-200 active:scale-95',
    success: 'bg-gradient-to-r from-success-500 to-success-600 text-white shadow-colored hover:shadow-colored-lg hover:scale-105 focus:ring-success-200 active:scale-95'
  }
  
  const sizes = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg',
    xl: 'px-10 py-5 text-xl'
  }
  
  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
    xl: 'w-7 h-7'
  }
  
  const classes = `${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`
  
  const renderIcon = () => {
    if (loading) {
      return (
        <div className={`${iconSizes[size]} border-2 border-current border-t-transparent rounded-full animate-spin`}></div>
      )
    }
    
    if (icon) {
      return React.cloneElement(icon, { className: iconSizes[size] })
    }
    
    return null
  }
  
  return (
    <button
      type={type}
      className={classes}
      disabled={disabled || loading}
      onClick={onClick}
      {...props}
    >
      {iconPosition === 'left' && renderIcon() && (
        <span className={children ? 'mr-2' : ''}>{renderIcon()}</span>
      )}
      
      {loading ? 'Loading...' : children}
      
      {iconPosition === 'right' && renderIcon() && (
        <span className={children ? 'ml-2' : ''}>{renderIcon()}</span>
      )}
    </button>
  )
}

// Icon Button variant
export const IconButton = ({ 
  children, 
  variant = 'ghost', 
  size = 'md', 
  className = '', 
  ...props 
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-xl transition-all duration-300 focus:outline-none focus:ring-4 disabled:opacity-50 disabled:cursor-not-allowed'
  
  const variants = {
    primary: 'bg-gradient-primary text-white shadow-colored hover:shadow-colored-lg hover:scale-105 focus:ring-primary-200 active:scale-95',
    secondary: 'bg-gradient-secondary text-white shadow-colored hover:shadow-colored-lg hover:scale-105 focus:ring-secondary-200 active:scale-95',
    outline: 'bg-white text-gray-700 border-2 border-gray-200 hover:border-primary-300 hover:text-primary-600 hover:bg-primary-50 shadow-soft hover:shadow-medium focus:ring-primary-200',
    ghost: 'text-gray-600 hover:text-primary-600 hover:bg-primary-50 focus:ring-primary-200',
    danger: 'bg-gradient-to-r from-error-500 to-error-600 text-white shadow-colored hover:shadow-colored-lg hover:scale-105 focus:ring-error-200 active:scale-95'
  }
  
  const sizes = {
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-base',
    lg: 'w-12 h-12 text-lg',
    xl: 'w-14 h-14 text-xl'
  }
  
  const classes = `${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`
  
  return (
    <button className={classes} {...props}>
      {children}
    </button>
  )
}

// Button Group component
export const ButtonGroup = ({ children, className = '' }) => {
  return (
    <div className={`inline-flex rounded-xl shadow-soft overflow-hidden ${className}`}>
      {React.Children.map(children, (child, index) => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child, {
            className: `${child.props.className || ''} ${
              index === 0 ? 'rounded-r-none' : 
              index === React.Children.count(children) - 1 ? 'rounded-l-none' : 
              'rounded-none'
            } border-r border-gray-200 last:border-r-0`
          })
        }
        return child
      })}
    </div>
  )
}
