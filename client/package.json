{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@clerk/clerk-react": "^5.23.0", "@tailwindcss/vite": "^4.0.9", "@vitejs/plugin-react": "^4.3.4", "axios": "^1.8.2", "humanize-duration": "^3.32.1", "lucide-react": "^0.537.0", "quill": "^2.0.3", "rc-progress": "^4.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.2.0", "react-toastify": "^11.0.5", "react-youtube": "^10.1.0", "tailwindcss": "^4.0.9", "uniqid": "^5.4.0"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "eslint": "^9.19.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "parcel": "^2.13.3", "vite": "^6.1.0"}}